<!-- 
    @component
    ## SimMid
    A component to display the middle section of the simulation test.
-->
<script module lang="ts">
    declare global {
        interface Window {
            MathJax: any;
        }
    }
</script>

<script lang='ts'>
	import * as UI from "../ui";
    import { AnnotateManager } from "$lib/annotate/annotateManager";
	import { untrack } from "svelte";

    let {
        currentQuestion,
        currentStudentData,
        setAnswer,
        setMarked,
        setCrossed,
        isMath,
        isCalculatorOpen,
        annotateManager = $bindable(),
        currentModuleIndex,
        currentModuleLength,
        currentQuestionIndex
    } = $props();

    let isSPR = $derived(!(currentQuestion.answerChoices));

    // Elimination tool
    let isEliminateTool = $state(false);

    function toggleElimination() {
        isEliminateTool = !isEliminateTool;
    }

    // Resize function
    let isResizing = $state(false);
    let offsetX = $state(0);
    let positionX  = $state("50%");
    let resizeBar: HTMLElement = $state();
    let leftSide: HTMLElement = $state();
    let windowWidth = $state(1000);

    function startResizing(e: MouseEvent) {
        isResizing = true;
        offsetX = e.clientX - resizeBar.offsetLeft;
        document.body.style.userSelect = "none";
        document.body.style.cursor = "col-resize";
    }

    // Annotate
    let middleSection: HTMLElement = $state();

    // Reinitialize Annotate Manager when module change
    $effect(() => {
        currentModuleIndex;
        untrack(() => {
            annotateManager = Array(currentModuleLength).fill(null).map(() => new AnnotateManager([middleSection]));
        });
    })

    // Update Annotate Manager when question change
    $effect(() => {
        annotateManager[currentQuestionIndex].setRoot([middleSection]);
    })

    // Render Latex again each time changing question
    $effect(() => {
        if (middleSection) {
            window.MathJax = {
                tex: {
                    inlineMath: [["$", "$"], ["\\(", "\\)"]],
                    displayMath: [["$$", "$$"], ["\\[", "\\]"]]
                },
                svg: { fontCache: "global" }
            };

            let script = document.createElement('script');
            script.src = "https://cdn.jsdelivr.net/npm/mathjax@4/tex-chtml.js";
            document.head.append(script);
        }
    });
</script>

<svelte:window bind:innerWidth={windowWidth} onresize={() => {
    if (leftSide.offsetWidth <= 460) positionX = `${((460 / windowWidth) * 100)}%`;
    if (windowWidth - leftSide.offsetWidth <= 460) positionX = `${((windowWidth - 460) / windowWidth) * 100}%`;
}} />

<svelte:document 
    onmousemove={(e) => {
        if (!isResizing) return;
        if (e.clientX < 460 || e.clientX > window.innerWidth - 460) return;
        positionX = `${((e.clientX - offsetX) / window.innerWidth) * 100}%`;
    }}
    onmouseup={() => {
        if (!isResizing) return;
        isResizing = false;
        document.body.style.userSelect = "";
        document.body.style.cursor = "";
    }}
/>

<!-- svelte-ignore a11y_no_static_element_interactions -->
{#key currentQuestion}
<div class="middle flex w-full h-full relative gap-[5px] justify-center leading-relaxed font-['Merriweather'] text-[#333]" bind:this={middleSection}>
    {#if isMath && !isSPR}
        <div class="middle-math flex-1 p-10 transition-transform duration-200 overflow-y-auto" class:translate-x-[200px]={isCalculatorOpen}>
            {@render question()}
        </div>
    {:else}
        <div class="left p-10 overflow-y-auto" style:width={positionX} bind:this={leftSide}>
            {#if isSPR}
                {@render leftSPR()}
            {:else}
                {@render leftVerbal()}
            {/if}
        </div>

        <div class="right flex-1 p-10 overflow-y-auto">
            {@render question()}
        </div>

        <div class="resize-bar w-[5px] h-full bg-[#D9D9D9] absolute cursor-col-resize" style:left={positionX} onmousedown={startResizing} bind:this={resizeBar}>
            <!-- The draggable thumb/handle -->
            <button class="absolute flex h-9 w-5 items-center justify-center rounded-md bg-gray-800 text-white border-white border-[3px] border-solid left-1/2 -translate-x-1/2 top-[20%] !cursor-grab active:!cursor-grabbing" >
                <svg
                    xmlns="http://www.w3.org/2000/svg"
                    viewBox="0 0 24 24"
                    fill="currentColor"
                >
                    <!-- Left pointing solid triangle -->
                    <path d="M10 6 L4 12 L10 18 Z"></path>
                    <!-- Right pointing solid triangle -->
                    <path d="M14 6 L20 12 L14 18 Z"></path>
                </svg>
            </button>
        </div>
    {/if}
</div>
{/key}

{#snippet leftSPR()}
{@const examples = [{answer: "3.5", acceptable: ["3.5", "3.50", "7/2"], unacceptable: ["31/2", "3 1/2"]},
{answer: "2/3", acceptable: ["2/3", ".6666", ".6667", "0.666", "0.667"], unacceptable: ["0.66", ".66", "0.67", ".67"]},
{answer: "-1/3", acceptable: ["-1/3", "-.3333", "-0.333"], unacceptable: ["-.33", "-0.33"]}]}
<div class="spr-instruction-container flex flex-col gap-4 items-center">
    <div class="spr-instruction-text">
        <h1 class="spr-instruction-header text-xl font-bold">Student-produced response directions</h1>
        <ul class="spr-instruction-list list-disc ml-10 mt-2">
            <li>If you find <b>more than one correct answer</b>, enter only one answer.</li>
            <li>You can enter up to 5 characters for a <b>positive</b> answer and up to 6 characters (including the negative sign) for a <b>negative</b> answer.</li>
            <li>If your answer is a <b>fraction</b> that doesn't fit in the provided space, enter the decimal equivalent.</li>
            <li>If your answer is a <b>decimal</b> that doesn't fit in the provided space, enter it by truncating or rounding at the fourth digit.</li>
            <li>If your answer is a <b>mixed number</b> (such as 3$1\over2$), enter it as an improper fraction (7/2) or its decimal equivalent (3.5).</li>
            <li>Don't enter <b>symbols</b> such as a percent sign, comma, or dollar sign.</li>
        </ul>
    </div>
    <p>Examples</p>
    <table class="spr-table w-3/4 text-center [&_td]:border-solid [&_td]:border-black [&_td]:border-[1px] [&_td]:px-3 [&_td]:py-5">
        <tbody>
            <tr>
                <td>Answer</td>
                <td>Acceptable ways to enter answer</td>
                <td>Unacceptable: will NOT receive credit</td>
            </tr>
            {#each examples as example}
                <tr>
                    <td>{example.answer}</td>
                    <td>
                        <div class="flex flex-col gap-2 items-center">
                            {#each example.acceptable as acceptable}
                                <p class="font-['Chivo'] text-sm bg-[#ededed]">{acceptable}</p>
                            {/each}
                        </div>
                    </td>
                    <td class="spr-table-chivo">
                        <div class="flex flex-col gap-2 items-center">
                            {#each example.unacceptable as unacceptable}
                                <p class="font-['Chivo'] text-sm bg-[#ededed]">{unacceptable}</p>
                            {/each}
                        </div>
                    </td>
                </tr>
            {/each}
        </tbody>
    </table>
</div>
{/snippet}

{#snippet leftVerbal()}
    <div class="left-verbal-container max-w-[780px] m-auto flex flex-col gap-4">
        <!-- Graph -->
        {#if currentQuestion.graph}
            <img class="m-auto" style:width={isMath ? "400px" : "90%"} src={currentQuestion.graph} alt="Figure">
        {/if}

        <!-- Student's Notes Question | Fiction Question -->
        {#if currentQuestion.intro}
            <div class="intro">{@html currentQuestion.intro}</div>

            <!-- Student's Notes Question-->
            {#if Array.isArray(currentQuestion.passage)}
                <ul class="ml-10 list-disc">
                    {#each currentQuestion.passage as point}
                        <li>{@html point}</li>            
                    {/each}
                </ul>

            <!-- Fiction Question -->
            {:else}
                <p class="ml-8">{@html currentQuestion.passage}</p>
            {/if}

        <!-- Paired Passage Question -->    
        {:else if currentQuestion.passage2}
            <div class="font-bold text-black">Text 1</div>
            <p>{@html currentQuestion.passage}</p>
            <div class="font-bold text-black mt-6">Text 2</div>
            <p>{@html currentQuestion.passage2}</p>

        <!-- Single Passage Question -->
        {:else}
            <p>{@html currentQuestion.passage}</p>
        {/if}
    </div>
{/snippet}

{#snippet question()}
    <div class="question-container max-w-[780px] flex flex-col gap-4 m-auto">
        <!-- Index, Mark, and Elimination Bar -->
        <UI.MarkBar {currentStudentData} {setMarked} {isEliminateTool} {toggleElimination} {isSPR}/>

        <!-- Question -->
        <div class="question-text ml-2">
            {@html currentQuestion.question}
        </div>

        {#if isSPR}
            <UI.SPRAnswerBox {currentStudentData} {setAnswer}/>
        {:else}
            <UI.MultipleChoices {currentQuestion} {currentStudentData} {setAnswer} {setCrossed} {isEliminateTool}/>
        {/if}
    </div>
{/snippet}

<style>
    /* Width */
    ::-webkit-scrollbar {
        width: 8px;
        height: 8px;
        margin: 4px 0;
    }

    /* Track */
    ::-webkit-scrollbar-track {
        background: #DAF8FF;
        border-radius: 5px;
    }

    /* Handle */
    ::-webkit-scrollbar-thumb {
        background: #66E2FF;
        border-radius: 5px;
    }
</style>