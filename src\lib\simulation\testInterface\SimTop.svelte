<!-- 
    @component
    ## SimTop
    A component to display the top section of the simulation test.
-->
<script module lang="ts">
    declare const katex: any;
</script>

<script lang="ts">
	import { Calculator, Reference, Breaking, AnnotateEditPopup } from '.';

    let {
        currentModuleIndex,
        moduleTitle,
        isMath,
        isCalculatorOpen,
        setCalculator,
        isReview,
        handleSubmitModule,
        isInModule,
        currentComponent,
        setCurrentComponent,
        currentAnnotateManager
    } = $props();


    let isDirectionOpen = $derived(currentComponent === 'direction');
    let isMoreOpen = $derived(currentComponent === 'more');
    let isBreakPopupOpen = $derived(currentComponent === 'break');
    let isBreaking = $derived(currentComponent === 'breaking');
    let isExitPopupOpen = $derived(currentComponent === 'exit');
    let isAnnoTipOpen = $derived(currentComponent === 'annoTip');
    let isAnnotateOpen = $derived(currentComponent === 'annotate');
    let isBreakConfirmed = $state(false);

    // Time
    const allotedTimes: number[] = [32, 32, 35, 35];

    // Timer state
	let seconds = $state(0);
	let minutes = $state(0);

	// Format time as MM:SS
	let formattedTime = $derived(`${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`);

	// Timer functionality
	let timerInterval = null;

	function startTimer(minute: number) {
		if (timerInterval) return;
        minutes = minute;
        seconds = 0;
		timerInterval = setInterval(() => {
			seconds--;
			if (seconds < 0) {
				seconds = 59;
				minutes--;
			}
		}, 1000);
	}

	function stopTimer() {
		if (timerInterval) {
			clearInterval(timerInterval);
			timerInterval = null;
		}
	}

    // Hide timer function
    let isTimerHidden = $state(false);

    function setTimerDisplay() {
        isTimerHidden = !isTimerHidden;
    }

    function setBreakConfirm() {
        isBreakConfirmed = !isBreakConfirmed;
    }

    // Reference
    let isReferenceOpen = $state(false);

    function setReference() {
        isReferenceOpen = !isReferenceOpen;
    }

    // Reset timer when module change
    $effect(() => {
        if (currentModuleIndex >= 0) {
            stopTimer();
            startTimer(allotedTimes[currentModuleIndex]);
        }
    })

    // Reset timer for breaking
    $effect(() => {
        if (!isInModule) {
            stopTimer();
            startTimer(10);
            currentComponent = 'breaking';
        } else {
            currentComponent = '';
        }
    })

    // Submit when time is up
    $effect(() => {
        if (minutes === 0 && seconds === 0) {
            stopTimer();
            setTimeout(() => {
                handleSubmitModule();
            }, 300);
        }
    })

    // Annotate
    let annoButton: HTMLButtonElement = $state(null);
    function handleAnnotateClick(e: MouseEvent) {
        e.stopPropagation();
        if (currentComponent === 'annotate') {
            setCurrentComponent('');
            return;
        }
        if (!currentAnnotateManager?.highlight()) {
            setCurrentComponent('annoTip');
        };
    }


    // Render latex
    $effect(() => {
        if (isDirectionOpen) {
            const latexElements = document.querySelectorAll('.latex');
            latexElements.forEach((element) => {
                katex.render(element.textContent, element, {
                    throwOnError: false
                });
            });
        }
    })
</script>

<svelte:head>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/katex@0.16.22/dist/katex.min.css" integrity="sha384-5TcZemv2l/9On385z///+d7MSYlvIEw9FuZTIdZ14vJLqWphw7e7ZPuOiCHJcFCP" crossorigin="anonymous">
    <script defer src="https://cdn.jsdelivr.net/npm/katex@0.16.22/dist/katex.min.js" integrity="sha384-cMkvdD8LoxVzGF/RPUKAcvmm49FQ0oxwDF3BGKtDXcEc+T1b2N+teh/OJfpU0jr6" crossorigin="anonymous"></script>
</svelte:head>
   
<!-- Top -->
<div class="top bg-white flex justify-between items-center border-b-[3px] border-[#505050] p-4 px-[64px] h-[90px] z-20 relative font-semibold font-['Inter']">
    <div class="top-left flex flex-col gap-2">
        <div class="title text-xl" style:font-family="Open Sans">{moduleTitle}</div>
        <button class="directions flex items-center hover:bg-[#e0e0e0] rounded-lg w-min px-1 -translate-x-1" onclick={(e) => {
            e.stopPropagation(); 
            setCurrentComponent('direction');
        }}>
            <div>Directions</div>
            <svg class:rotate-180={!isDirectionOpen} xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none">
                <path d="M12.71 15.5397L18.36 9.87974C18.4537 9.78677 18.5281 9.67617 18.5789 9.55431C18.6296 9.43246 18.6558 9.30175 18.6558 9.16974C18.6558 9.03773 18.6296 8.90702 18.5789 8.78516C18.5281 8.6633 18.4537 8.5527 18.36 8.45974C18.1726 8.27349 17.9191 8.16895 17.655 8.16895C17.3908 8.16895 17.1373 8.27349 16.95 8.45974L11.95 13.4097L6.99996 8.45974C6.8126 8.27349 6.55915 8.16895 6.29496 8.16895C6.03078 8.16895 5.77733 8.27349 5.58996 8.45974C5.49548 8.55235 5.42031 8.6628 5.36881 8.78467C5.31731 8.90655 5.29051 9.03743 5.28996 9.16974C5.29051 9.30204 5.31731 9.43293 5.36881 9.5548C5.42031 9.67668 5.49548 9.78712 5.58996 9.87974L11.24 15.5397C11.3336 15.6412 11.4473 15.7223 11.5738 15.7777C11.7003 15.8331 11.8369 15.8617 11.975 15.8617C12.1131 15.8617 12.2497 15.8331 12.3762 15.7777C12.5027 15.7223 12.6163 15.6412 12.71 15.5397Z" fill="black"/>
            </svg>
        </button>
    </div>

    <div class="clock flex flex-col gap-2 items-center absolute left-1/2 -translate-x-1/2">
        {#if isTimerHidden}
            <svg xmlns="http://www.w3.org/2000/svg" width="27.5" height="27.5" viewBox="0 0 24 24" fill="none">
                    <path d="M18.3 8.59L19.21 7.69C19.3983 7.5017 19.5041 7.2463 19.5041 6.98C19.5041 6.7137 19.3983 6.4583 19.21 6.27C19.0217 6.0817 18.7663 5.97591 18.5 5.97591C18.2337 5.97591 17.9783 6.0817 17.79 6.27L16.89 7.18C15.4886 6.09585 13.7669 5.50764 11.995 5.50764C10.2232 5.50764 8.50147 6.09585 7.10003 7.18L6.19003 6.26C6.0004 6.0717 5.74373 5.96644 5.47649 5.96737C5.20925 5.96831 4.95333 6.07537 4.76503 6.265C4.57672 6.45463 4.47146 6.7113 4.4724 6.97854C4.47334 7.24578 4.5804 7.5017 4.77003 7.69L5.69003 8.6C4.59304 9.99755 3.99782 11.7233 4.00003 13.5C3.99676 14.7754 4.29849 16.0331 4.88005 17.1683C5.46161 18.3034 6.30614 19.283 7.34322 20.0254C8.38029 20.7679 9.57985 21.2516 10.8418 21.4362C12.1038 21.6208 13.3917 21.5011 14.598 21.0869C15.8043 20.6727 16.8941 19.9761 17.7764 19.0552C18.6588 18.1342 19.3082 17.0157 19.6705 15.7928C20.0328 14.5699 20.0974 13.2781 19.859 12.0251C19.6206 10.7722 19.0861 9.5944 18.3 8.59ZM12 19.5C10.8133 19.5 9.6533 19.1481 8.6666 18.4888C7.67991 17.8295 6.91087 16.8925 6.45675 15.7961C6.00262 14.6997 5.8838 13.4933 6.11531 12.3295C6.34683 11.1656 6.91827 10.0965 7.75739 9.25736C8.5965 8.41824 9.6656 7.8468 10.8295 7.61529C11.9934 7.38378 13.1998 7.5026 14.2961 7.95672C15.3925 8.41085 16.3296 9.17988 16.9888 10.1666C17.6481 11.1533 18 12.3133 18 13.5C18 15.0913 17.3679 16.6174 16.2427 17.7426C15.1174 18.8679 13.5913 19.5 12 19.5ZM10 4.5H14C14.2652 4.5 14.5196 4.39464 14.7071 4.20711C14.8947 4.01957 15 3.76522 15 3.5C15 3.23478 14.8947 2.98043 14.7071 2.79289C14.5196 2.60536 14.2652 2.5 14 2.5H10C9.73481 2.5 9.48046 2.60536 9.29292 2.79289C9.10538 2.98043 9.00003 3.23478 9.00003 3.5C9.00003 3.76522 9.10538 4.01957 9.29292 4.20711C9.48046 4.39464 9.73481 4.5 10 4.5ZM13 10.5C13 10.2348 12.8947 9.98043 12.7071 9.79289C12.5196 9.60536 12.2652 9.5 12 9.5C11.7348 9.5 11.4805 9.60536 11.2929 9.79289C11.1054 9.98043 11 10.2348 11 10.5V12.39C10.7736 12.5925 10.614 12.859 10.5423 13.1542C10.4707 13.4495 10.4904 13.7595 10.5988 14.0433C10.7072 14.3271 10.8992 14.5712 11.1494 14.7435C11.3996 14.9158 11.6962 15.008 12 15.008C12.3038 15.008 12.6004 14.9158 12.8507 14.7435C13.1009 14.5712 13.2929 14.3271 13.4013 14.0433C13.5097 13.7595 13.5294 13.4495 13.4577 13.1542C13.3861 12.859 13.2265 12.5925 13 12.39V10.5Z" fill="#888888"/>
            </svg>
        {:else}
            <div class="font-semibold text-xl">{formattedTime}</div>
        {/if}
        <button class="set-timer-display-button w-[70px] border-black rounded-full border-solid py-[3px] select-none hover:bg-[#e6e6e6]" onclick={setTimerDisplay}>
            <div class="text-xs">{isTimerHidden ? "Show" : "Hide"}</div>
        </button>
    </div>

    <div class="tools flex gap-4 select-none text-xs">
        {#if !isReview}
            {#if !isMath}
                <button class="tool-button flex flex-col items-center justify-center gap-1 hover:bg-[#e0e0e0] p-1 rounded-lg relative" class:tool-active={isAnnotateOpen} class:rounded-b-none={isAnnotateOpen} onclick={handleAnnotateClick} bind:this={annoButton}>
                    <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none">
                        <path d="M22 7.24002C22.0008 7.10841 21.9756 6.97795 21.9258 6.85611C21.876 6.73427 21.8027 6.62346 21.71 6.53002L17.47 2.29002C17.3766 2.19734 17.2658 2.12401 17.1439 2.07425C17.0221 2.02448 16.8916 1.99926 16.76 2.00002C16.6284 1.99926 16.4979 2.02448 16.3761 2.07425C16.2543 2.12401 16.1435 2.19734 16.05 2.29002L13.22 5.12002L2.29002 16.05C2.19734 16.1435 2.12401 16.2543 2.07425 16.3761C2.02448 16.4979 1.99926 16.6284 2.00002 16.76V21C2.00002 21.2652 2.10537 21.5196 2.29291 21.7071C2.48045 21.8947 2.7348 22 3.00002 22H7.24002C7.37994 22.0076 7.51991 21.9857 7.65084 21.9358C7.78176 21.8858 7.90073 21.8089 8.00002 21.71L18.87 10.78L21.71 8.00002C21.8013 7.9031 21.8757 7.79155 21.93 7.67002C21.9397 7.59031 21.9397 7.50973 21.93 7.43002C21.9347 7.38347 21.9347 7.33657 21.93 7.29002L22 7.24002ZM6.83002 20H4.00002V17.17L13.93 7.24002L16.76 10.07L6.83002 20ZM18.17 8.66002L15.34 5.83002L16.76 4.42002L19.58 7.24002L18.17 8.66002Z" fill="black"/>
                    </svg>
                    <div class="tool-text">Annotate</div>
                </button>
            {:else}
                <!-- Calculator -->
                <button class="tool-button flex flex-col items-center justify-center gap-1 hover:bg-[#e0e0e0] p-1 rounded-lg relative" class:tool-active={isCalculatorOpen} class:rounded-b-none={isCalculatorOpen} onclick={setCalculator}>
                    <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none">
                        <path d="M12.71 17.29C12.6608 17.2448 12.6072 17.2046 12.55 17.17C12.4989 17.1304 12.4414 17.1 12.38 17.08C12.3205 17.0496 12.2562 17.0293 12.19 17.02C11.9984 16.9795 11.7989 17.0005 11.62 17.08C11.4959 17.1249 11.3832 17.1966 11.2899 17.2899C11.1966 17.3832 11.1249 17.4959 11.08 17.62C11.024 17.7387 10.9966 17.8688 11 18C10.9984 18.13 11.0222 18.2591 11.07 18.38C11.128 18.4995 11.202 18.6105 11.29 18.71C11.3834 18.8027 11.4943 18.876 11.6161 18.9258C11.7379 18.9755 11.8684 19.0008 12 19C12.1312 19.0034 12.2613 18.976 12.38 18.92C12.5012 18.8694 12.6128 18.7983 12.71 18.71C12.8027 18.6166 12.876 18.5057 12.9258 18.3839C12.9755 18.2621 13.0008 18.1316 13 18C12.9984 17.8693 12.9712 17.7402 12.92 17.62C12.8724 17.4972 12.801 17.3851 12.71 17.29ZM8.55 13.17C8.49895 13.1304 8.44143 13.1 8.38 13.08C8.32176 13.0426 8.25747 13.0155 8.19 13C8.06172 12.9701 7.92828 12.9701 7.8 13L7.62 13.06L7.44 13.15L7.29 13.27C7.10253 13.4667 6.99858 13.7283 7 14C6.99924 14.1316 7.02446 14.2621 7.07423 14.3839C7.12399 14.5057 7.19732 14.6166 7.29 14.71C7.3872 14.7983 7.49882 14.8694 7.62 14.92C7.7715 14.9822 7.93597 15.0063 8.09895 14.9901C8.26193 14.9739 8.41843 14.9179 8.55471 14.8271C8.69099 14.7362 8.80287 14.6133 8.88051 14.4691C8.95815 14.3249 8.99918 14.1638 9 14C8.99632 13.7352 8.89273 13.4816 8.71 13.29L8.55 13.17ZM8.71 17.29C8.6149 17.199 8.50275 17.1276 8.38 17.08C8.19898 16.9961 7.99698 16.9682 7.8 17L7.62 17.06C7.55628 17.0819 7.49575 17.1122 7.44 17.15C7.38761 17.1869 7.33752 17.227 7.29 17.27C7.19896 17.3651 7.12759 17.4772 7.08 17.6C7.02709 17.7197 6.99977 17.8491 6.99977 17.98C6.99977 18.1109 7.02709 18.2403 7.08 18.36C7.13064 18.4812 7.20167 18.5928 7.29 18.69C7.38178 18.7863 7.49185 18.8633 7.61376 18.9165C7.73567 18.9698 7.86698 18.9981 8 19C8.13118 19.0034 8.26132 18.976 8.38 18.92C8.50118 18.8694 8.6128 18.7983 8.71 18.71C8.79833 18.6128 8.86936 18.5012 8.92 18.38C8.97291 18.2603 9.00023 18.1309 9.00023 18C9.00023 17.8691 8.97291 17.7397 8.92 17.62C8.87241 17.4972 8.80104 17.3851 8.71 17.29ZM11.62 13.08C11.4972 13.1276 11.3851 13.199 11.29 13.29C11.1073 13.4816 11.0037 13.7352 11 14C11.0008 14.1638 11.0418 14.3249 11.1195 14.4691C11.1971 14.6133 11.309 14.7362 11.4453 14.8271C11.5816 14.9179 11.7381 14.9739 11.9011 14.9901C12.064 15.0063 12.2285 14.9822 12.38 14.92C12.5012 14.8694 12.6128 14.7983 12.71 14.71C12.8027 14.6166 12.876 14.5057 12.9258 14.3839C12.9755 14.2621 13.0008 14.1316 13 14C12.9963 13.7352 12.8927 13.4816 12.71 13.29C12.5694 13.1512 12.3908 13.0572 12.1968 13.0199C12.0028 12.9825 11.8021 13.0034 11.62 13.08ZM16.71 17.29C16.6128 17.2017 16.5012 17.1306 16.38 17.08C16.1979 17.0034 15.9972 16.9825 15.8032 17.0199C15.6092 17.0572 15.4306 17.1512 15.29 17.29C15.199 17.3851 15.1276 17.4972 15.08 17.62C15.0271 17.7397 14.9998 17.8691 14.9998 18C14.9998 18.1309 15.0271 18.2603 15.08 18.38C15.1306 18.5012 15.2017 18.6128 15.29 18.71C15.3834 18.8027 15.4943 18.876 15.6161 18.9258C15.7379 18.9755 15.8684 19.0008 16 19C16.1312 19.0034 16.2613 18.976 16.38 18.92C16.5012 18.8694 16.6128 18.7983 16.71 18.71C16.8488 18.5694 16.9428 18.3908 16.9801 18.1968C17.0175 18.0028 16.9966 17.8021 16.92 17.62C16.8724 17.4972 16.801 17.3851 16.71 17.29ZM16 5H8C7.73478 5 7.48043 5.10536 7.29289 5.29289C7.10536 5.48043 7 5.73478 7 6V10C7 10.2652 7.10536 10.5196 7.29289 10.7071C7.48043 10.8946 7.73478 11 8 11H16C16.2652 11 16.5196 10.8946 16.7071 10.7071C16.8946 10.5196 17 10.2652 17 10V6C17 5.73478 16.8946 5.48043 16.7071 5.29289C16.5196 5.10536 16.2652 5 16 5ZM15 9H9V7H15V9ZM18 1H6C5.20435 1 4.44129 1.31607 3.87868 1.87868C3.31607 2.44129 3 3.20435 3 4V20C3 20.7956 3.31607 21.5587 3.87868 22.1213C4.44129 22.6839 5.20435 23 6 23H18C18.7956 23 19.5587 22.6839 20.1213 22.1213C20.6839 21.5587 21 20.7956 21 20V4C21 3.20435 20.6839 2.44129 20.1213 1.87868C19.5587 1.31607 18.7956 1 18 1ZM19 20C19 20.2652 18.8946 20.5196 18.7071 20.7071C18.5196 20.8946 18.2652 21 18 21H6C5.73478 21 5.48043 20.8946 5.29289 20.7071C5.10536 20.5196 5 20.2652 5 20V4C5 3.73478 5.10536 3.48043 5.29289 3.29289C5.48043 3.10536 5.73478 3 6 3H18C18.2652 3 18.5196 3.10536 18.7071 3.29289C18.8946 3.48043 19 3.73478 19 4V20ZM16.55 13.17C16.4989 13.1304 16.4414 13.1 16.38 13.08C16.3205 13.0496 16.2562 13.0293 16.19 13.02C16.0617 12.9901 15.9283 12.9901 15.8 13.02L15.62 13.08L15.44 13.17L15.29 13.29C15.1073 13.4816 15.0037 13.7352 15 14C15.0008 14.1638 15.0418 14.3249 15.1195 14.4691C15.1971 14.6133 15.309 14.7362 15.4453 14.8271C15.5816 14.9179 15.7381 14.9739 15.9011 14.9901C16.064 15.0063 16.2285 14.9822 16.38 14.92C16.5012 14.8694 16.6128 14.7983 16.71 14.71C16.8027 14.6166 16.876 14.5057 16.9258 14.3839C16.9755 14.2621 17.0008 14.1316 17 14C16.9963 13.7352 16.8927 13.4816 16.71 13.29L16.55 13.17Z" fill="black"/>
                    </svg>
                    <div class="tool-text">Calculator</div>
                </button>

                <!-- Reference -->
                <button class="tool-button flex flex-col items-center justify-center gap-1 hover:bg-[#e0e0e0] p-1 rounded-lg relative" class:tool-active={isReferenceOpen} class:rounded-b-none={isReferenceOpen} onclick={setReference}>
                    <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none">
                        <path d="M5.5 8H6V8.5C6 8.76522 6.10536 9.01957 6.29289 9.20711C6.48043 9.39464 6.73478 9.5 7 9.5C7.26522 9.5 7.51957 9.39464 7.70711 9.20711C7.89464 9.01957 8 8.76522 8 8.5V8H8.5C8.76522 8 9.01957 7.89464 9.20711 7.70711C9.39464 7.51957 9.5 7.26522 9.5 7C9.5 6.73478 9.39464 6.48043 9.20711 6.29289C9.01957 6.10536 8.76522 6 8.5 6H8V5.5C8 5.23478 7.89464 4.98043 7.70711 4.79289C7.51957 4.60536 7.26522 4.5 7 4.5C6.73478 4.5 6.48043 4.60536 6.29289 4.79289C6.10536 4.98043 6 5.23478 6 5.5V6H5.5C5.23478 6 4.98043 6.10536 4.79289 6.29289C4.60536 6.48043 4.5 6.73478 4.5 7C4.5 7.26522 4.60536 7.51957 4.79289 7.70711C4.98043 7.89464 5.23478 8 5.5 8ZM4.88 19.12C5.06736 19.3063 5.32081 19.4108 5.585 19.4108C5.84919 19.4108 6.10264 19.3063 6.29 19.12L7 18.41L7.71 19.12C7.89736 19.3063 8.15081 19.4108 8.415 19.4108C8.67919 19.4108 8.93264 19.3063 9.12 19.12C9.30625 18.9326 9.41079 18.6792 9.41079 18.415C9.41079 18.1508 9.30625 17.8974 9.12 17.71L8.41 17L9.12 16.29C9.28383 16.0987 9.36943 15.8526 9.35971 15.6009C9.34999 15.3493 9.24566 15.1105 9.06756 14.9324C8.88947 14.7543 8.65073 14.65 8.39905 14.6403C8.14738 14.6306 7.9013 14.7162 7.71 14.88L7 15.59L6.29 14.88C6.0987 14.7162 5.85262 14.6306 5.60095 14.6403C5.34927 14.65 5.11053 14.7543 4.93244 14.9324C4.75434 15.1105 4.65001 15.3493 4.64029 15.6009C4.63057 15.8526 4.71617 16.0987 4.88 16.29L5.59 17L4.88 17.71C4.69375 17.8974 4.58921 18.1508 4.58921 18.415C4.58921 18.6792 4.69375 18.9326 4.88 19.12ZM20 1H4C3.20435 1 2.44129 1.31607 1.87868 1.87868C1.31607 2.44129 1 3.20435 1 4V20C1 20.7956 1.31607 21.5587 1.87868 22.1213C2.44129 22.6839 3.20435 23 4 23H20C20.7956 23 21.5587 22.6839 22.1213 22.1213C22.6839 21.5587 23 20.7956 23 20V4C23 3.20435 22.6839 2.44129 22.1213 1.87868C21.5587 1.31607 20.7956 1 20 1ZM11 21H4C3.73478 21 3.48043 20.8946 3.29289 20.7071C3.10536 20.5196 3 20.2652 3 20V13H11V21ZM11 11H3V4C3 3.73478 3.10536 3.48043 3.29289 3.29289C3.48043 3.10536 3.73478 3 4 3H11V11ZM21 20C21 20.2652 20.8946 20.5196 20.7071 20.7071C20.5196 20.8946 20.2652 21 20 21H13V13H21V20ZM21 11H13V3H20C20.2652 3 20.5196 3.10536 20.7071 3.29289C20.8946 3.48043 21 3.73478 21 4V11ZM15.5 16.5H18.5C18.7652 16.5 19.0196 16.3946 19.2071 16.2071C19.3946 16.0196 19.5 15.7652 19.5 15.5C19.5 15.2348 19.3946 14.9804 19.2071 14.7929C19.0196 14.6054 18.7652 14.5 18.5 14.5H15.5C15.2348 14.5 14.9804 14.6054 14.7929 14.7929C14.6054 14.9804 14.5 15.2348 14.5 15.5C14.5 15.7652 14.6054 16.0196 14.7929 16.2071C14.9804 16.3946 15.2348 16.5 15.5 16.5ZM18.5 6H15.5C15.2348 6 14.9804 6.10536 14.7929 6.29289C14.6054 6.48043 14.5 6.73478 14.5 7C14.5 7.26522 14.6054 7.51957 14.7929 7.70711C14.9804 7.89464 15.2348 8 15.5 8H18.5C18.7652 8 19.0196 7.89464 19.2071 7.70711C19.3946 7.51957 19.5 7.26522 19.5 7C19.5 6.73478 19.3946 6.48043 19.2071 6.29289C19.0196 6.10536 18.7652 6 18.5 6ZM15.5 19.5H18.5C18.7652 19.5 19.0196 19.3946 19.2071 19.2071C19.3946 19.0196 19.5 18.7652 19.5 18.5C19.5 18.2348 19.3946 17.9804 19.2071 17.7929C19.0196 17.6054 18.7652 17.5 18.5 17.5H15.5C15.2348 17.5 14.9804 17.6054 14.7929 17.7929C14.6054 17.9804 14.5 18.2348 14.5 18.5C14.5 18.7652 14.6054 19.0196 14.7929 19.2071C14.9804 19.3946 15.2348 19.5 15.5 19.5Z" fill="black"/>
                    </svg>
                    <div class="tool-text">Reference</div>
                </button>
            {/if}
        {/if}

        <button class="more-button flex flex-col items-center justify-center gap-1 hover:bg-[#e0e0e0] p-1 rounded-lg relative" class:tool-active={isMoreOpen} class:rounded-b-none={isMoreOpen} onclick={(e) => {
            e.stopPropagation();
            setCurrentComponent('more');
        }}>
            <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none">
                <path d="M12 7C12.3956 7 12.7822 6.8827 13.1111 6.66294C13.44 6.44318 13.6964 6.13082 13.8478 5.76537C13.9991 5.39992 14.0387 4.99778 13.9616 4.60982C13.8844 4.22186 13.6939 3.86549 13.4142 3.58579C13.1345 3.30608 12.7781 3.1156 12.3902 3.03843C12.0022 2.96126 11.6001 3.00087 11.2346 3.15224C10.8692 3.30362 10.5568 3.55996 10.3371 3.88886C10.1173 4.21776 10 4.60444 10 5C10 5.53043 10.2107 6.03914 10.5858 6.41421C10.9609 6.78929 11.4696 7 12 7ZM12 17C11.6044 17 11.2178 17.1173 10.8889 17.3371C10.56 17.5568 10.3036 17.8692 10.1522 18.2346C10.0009 18.6001 9.96126 19.0022 10.0384 19.3902C10.1156 19.7781 10.3061 20.1345 10.5858 20.4142C10.8655 20.6939 11.2219 20.8844 11.6098 20.9616C11.9978 21.0387 12.3999 20.9991 12.7654 20.8478C13.1308 20.6964 13.4432 20.44 13.6629 20.1111C13.8827 19.7822 14 19.3956 14 19C14 18.4696 13.7893 17.9609 13.4142 17.5858C13.0391 17.2107 12.5304 17 12 17ZM12 10C11.6044 10 11.2178 10.1173 10.8889 10.3371C10.56 10.5568 10.3036 10.8692 10.1522 11.2346C10.0009 11.6001 9.96126 12.0022 10.0384 12.3902C10.1156 12.7781 10.3061 13.1345 10.5858 13.4142C10.8655 13.6939 11.2219 13.8844 11.6098 13.9616C11.9978 14.0387 12.3999 13.9991 12.7654 13.8478C13.1308 13.6964 13.4432 13.44 13.6629 13.1111C13.8827 12.7822 14 12.3956 14 12C14 11.4696 13.7893 10.9609 13.4142 10.5858C13.0391 10.2107 12.5304 10 12 10Z" fill="black"/>
            </svg>
            <div class="tool-text">More</div>
        </button>
    </div>
</div>

<!-- svelte-ignore a11y_no_static_element_interactions -->
<!-- svelte-ignore a11y_click_events_have_key_events -->

<!-- Direction -->
{#if isDirectionOpen}
<div class="direction-background fixed inset-0 bg-black/30 z-10">
    <div class="direction-container bg-white w-[70%] h-[80%] fixed top-[100px] left-[55px] flex flex-col py-4 px-1 gap-4" onclick={(e) => {e.stopPropagation();}}>
        <div class="direction-text text-[#333] font-['Merriweather'] flex-1 overflow-y-auto flex flex-col gap-4 leading-relaxed pt-5 px-10">
            {#if !isMath}
                <p>
                    The questions in this section address a number of important reading and writing skills. Each question includes one or more passages, 
                    which may include a table or graph. Read each passage and question carefully, and then choose the best answer to the question based 
                    on the passage(s).
                </p>
                <p>
                    All questions in this section are multiple-choice with four answer choices. Each question has a single best 
                    answer.
                </p>
            {:else}
            {@const examples = [{answer: "3.5", acceptable: ["3.5", "3.50", "7/2"], unacceptable: ["31/2", "3 1/2"]}, {answer: "2/3", acceptable: ["2/3", ".6666", ".6667", "0.666", "0.667"], unacceptable: ["0.66", ".66", "0.67", ".67"]}, {answer: "-1/3", acceptable: ["-1/3", "-.3333", "-0.333"], unacceptable: ["-.33", "-0.33"]}]}
                <p>
                    The questions in this section address a number of important skills.
                </p>
                <p>
                    Use of a calculator is permitted for all questions. A reference sheet, calculator, and these directions can be accessed throughout the test.<br>
                </p> 
                <p>
                    Unless otherwise indicated:
                </p> 
                    <ul class="ml-6 list-disc">
                        <li>All variables and expressions represented real numbers.</li>
                        <li>Figures provided are drawn to scale.</li>
                        <li>All figures lie in a plane.</li>
                        <li>The domain of a given function f is the set of all real numbers x for which f(x) is a real number.</li>
                    </ul>
                <p>
                    For <b>multiple-choice questions</b>, solve each problem and choose the correct answer from the choices provided. Each multiple-choice question has a single correct answer.
                </p>  
                <p>
                    For <b>student-produced response questions</b>, solve each problem and enter your answer as described below.
                </p>        
                <ul class="ml-6 list-disc">
                    <li>If you find <b>more than one correct answer</b>, enter only one answer.</li>
                    <li>You can enter up to 5 characters for a <b>positive</b> answer and up to 6 characters (including the negative sign) for a <b>negative</b> answer.</li>
                    <li>If your answer is a <b>fraction</b> that doesn't fit in the provided space, enter the decimal equivalent.</li>
                    <li>If your answer is a <b>decimal</b> that doesn't fit in the provided space, enter it by truncating or rounding at the fourth digit.</li>
                    <li>If your answer is a <b>mixed number</b> (such as 3<span class="latex">1\over2</span>), enter it as an improper fraction (7/2) or its decimal equivalent (3.5).</li>
                    <li>Don't enter <b>symbols</b> such as a percent sign, comma, or dollar sign.</li>
                </ul>
                <p class="text-center">Examples</p>
                <table class="spr-table w-3/4 text-center [&_td]:border-solid [&_td]:border-black [&_td]:border-[1px] [&_td]:px-3 [&_td]:py-5 m-auto w-1/2">
                    <tbody>
                        <tr>
                            <td>Answer</td>
                            <td>Acceptable ways to enter answer</td>
                            <td>Unacceptable: will NOT receive credit</td>
                        </tr>
                        {#each examples as example}
                            <tr>
                                <td>{example.answer}</td>
                                <td>
                                    <div class="flex flex-col gap-2 items-center">
                                        {#each example.acceptable as acceptable}
                                            <p class="font-['Chivo'] text-sm bg-[#ededed]">{acceptable}</p>
                                        {/each}
                                    </div>
                                </td>
                                <td class="spr-table-chivo">
                                    <div class="flex flex-col gap-2 items-center">
                                        {#each example.unacceptable as unacceptable}
                                            <p class="font-['Chivo'] text-sm bg-[#ededed]">{unacceptable}</p>
                                        {/each}
                                    </div>
                                </td>
                            </tr>
                        {/each}
                    </tbody>
                </table>
            {/if}
        </div>
        <div class="flex justify-end">
            <button class="direction-button select-none bg-[#FFC800] border-solid border-black rounded-full px-6 py-3 font-['Inter'] font-semibold mr-5 hover:bg-[#deab02]" onclick={() => setCurrentComponent('')}>Close</button>
        </div>
    </div>
</div>
{/if}


<!-- svelte-ignore a11y_no_static_element_interactions -->
<!-- svelte-ignore a11y_click_events_have_key_events -->

<!-- Annotate -->
{#if isAnnoTipOpen}
    <div class="anno-tip fixed top-[93px] right-[28px] z-20 flex flex-col bg-[#505050] rounded-lg shadow overflow-hidden p-4 text-[#FFF] select-none" onclick={(e) => e.stopPropagation()}>
        <div style:font-weight={700}>MAKE A SELECTION FIRST</div>
        <div style:font-weight={400}>Select some text, then press <b>Annotate</b>.</div>
    </div>
{/if}

<!-- svelte-ignore a11y_no_static_element_interactions -->
<!-- svelte-ignore a11y_click_events_have_key_events -->

<!-- More -->
{#if isMoreOpen}
<div class="more-prompt fixed top-[93px] right-[48px] z-50 flex flex-col bg-[#FFF] rounded-[10px] shadow overflow-hidden font-['Inter']" onclick={(e) => e.stopPropagation()}>
    <button class="more-option flex gap-4 px-6 py-5 hover:bg-[#F0F0F0] hover:underline text-lg items-center" onclick={() => setCurrentComponent('break')}>
        <svg xmlns="http://www.w3.org/2000/svg" width="22" height="22" viewBox="0 0 22 22" fill="none">
            <path d="M3.66699 10.9997C3.66699 11.2428 3.76357 11.4759 3.93548 11.6479C4.10739 11.8198 4.34054 11.9163 4.58366 11.9163H11.5412L9.43283 14.0155C9.34691 14.1007 9.27871 14.2021 9.23218 14.3138C9.18564 14.4255 9.16168 14.5453 9.16168 14.6663C9.16168 14.7874 9.18564 14.9072 9.23218 15.0189C9.27871 15.1306 9.34691 15.232 9.43283 15.3172C9.51804 15.4031 9.61943 15.4713 9.73113 15.5178C9.84283 15.5644 9.96265 15.5883 10.0837 15.5883C10.2047 15.5883 10.3245 15.5644 10.4362 15.5178C10.5479 15.4713 10.6493 15.4031 10.7345 15.3172L14.4012 11.6505C14.4846 11.5633 14.55 11.4605 14.5937 11.348C14.6853 11.1248 14.6853 10.8745 14.5937 10.6513C14.55 10.5388 14.4846 10.436 14.4012 10.3488L10.7345 6.68217C10.649 6.59671 10.5476 6.52891 10.4359 6.48265C10.3242 6.4364 10.2045 6.41259 10.0837 6.41259C9.96279 6.41259 9.8431 6.4364 9.73143 6.48265C9.61976 6.52891 9.51829 6.59671 9.43283 6.68217C9.34736 6.76764 9.27956 6.86911 9.2333 6.98078C9.18705 7.09245 9.16324 7.21214 9.16324 7.33301C9.16324 7.45388 9.18705 7.57357 9.2333 7.68524C9.27956 7.79691 9.34736 7.89837 9.43283 7.98384L11.5412 10.083H4.58366C4.34054 10.083 4.10739 10.1796 3.93548 10.3515C3.76357 10.5234 3.66699 10.7566 3.66699 10.9997ZM15.5837 1.83301H6.41699C5.68765 1.83301 4.98817 2.12274 4.47245 2.63846C3.95672 3.15419 3.66699 3.85366 3.66699 4.58301V7.33301C3.66699 7.57612 3.76357 7.80928 3.93548 7.98119C4.10739 8.1531 4.34054 8.24967 4.58366 8.24967C4.82677 8.24967 5.05993 8.1531 5.23184 7.98119C5.40375 7.80928 5.50033 7.57612 5.50033 7.33301V4.58301C5.50033 4.33989 5.5969 4.10673 5.76881 3.93483C5.94072 3.76292 6.17388 3.66634 6.41699 3.66634H15.5837C15.8268 3.66634 16.0599 3.76292 16.2318 3.93483C16.4037 4.10673 16.5003 4.33989 16.5003 4.58301V17.4163C16.5003 17.6595 16.4037 17.8926 16.2318 18.0645C16.0599 18.2364 15.8268 18.333 15.5837 18.333H6.41699C6.17388 18.333 5.94072 18.2364 5.76881 18.0645C5.5969 17.8926 5.50033 17.6595 5.50033 17.4163V14.6663C5.50033 14.4232 5.40375 14.1901 5.23184 14.0182C5.05993 13.8463 4.82677 13.7497 4.58366 13.7497C4.34054 13.7497 4.10739 13.8463 3.93548 14.0182C3.76357 14.1901 3.66699 14.4232 3.66699 14.6663V17.4163C3.66699 18.1457 3.95672 18.8452 4.47245 19.3609C4.98817 19.8766 5.68765 20.1663 6.41699 20.1663H15.5837C16.313 20.1663 17.0125 19.8766 17.5282 19.3609C18.0439 18.8452 18.3337 18.1457 18.3337 17.4163V4.58301C18.3337 3.85366 18.0439 3.15419 17.5282 2.63846C17.0125 2.12274 16.313 1.83301 15.5837 1.83301Z" fill="black"/>
        </svg>
        <span>Unscheduled Break</span>
    </button>
    <button class="more-option flex gap-4 px-6 py-5 hover:bg-[#F0F0F0] hover:underline text-lg items-center" onclick={() => setCurrentComponent('exit')}>
        <svg width="20" height="19" viewBox="0 0 20 19" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path d="M10.9038 5.87358L10.7514 12.549H9.26491L9.11719 5.87358H10.9038ZM10.0082 15.4297C9.7281 15.4297 9.48804 15.3312 9.288 15.1342C9.09103 14.9373 8.99254 14.6972 8.99254 14.4141C8.99254 14.1371 9.09103 13.9001 9.288 13.7031C9.48804 13.5062 9.7281 13.4077 10.0082 13.4077C10.2821 13.4077 10.5191 13.5062 10.7191 13.7031C10.9222 13.9001 11.0238 14.1371 11.0238 14.4141C11.0238 14.6018 10.9761 14.7726 10.8807 14.9265C10.7884 15.0804 10.6652 15.2035 10.5114 15.2958C10.3606 15.3851 10.1928 15.4297 10.0082 15.4297Z" fill="black"/>
            <path fill-rule="evenodd" clip-rule="evenodd" d="M10 2.94571L2.12321 16.5887H17.8768L10 2.94571ZM10.9846 1.24033C10.547 0.482389 9.453 0.482389 9.0154 1.24033L0.154014 16.5887C-0.283585 17.3467 0.263413 18.2941 1.13861 18.2941H18.8614C19.7366 18.2941 20.2836 17.3467 19.846 16.5887L10.9846 1.24033Z" fill="black"/>
        </svg>
        <span>Exit the Exam</span>
    </button>
</div>
{/if}

<!-- svelte-ignore a11y_no_static_element_interactions -->
<!-- svelte-ignore a11y_click_events_have_key_events -->

<!-- Break -->
{#if isBreakPopupOpen}
<div class="break-overlay fixed inset-0 bg-black/80 z-50 flex justify-center items-center" onclick={(e) => e.stopPropagation()}>
    <div class="break-popup bg-white flex flex-col gap-6 py-10 px-12 font-['Inter'] rounded-xl">
        <div class="break-title text-2xl">You'll Lose Testing Time During This Break</div>
        <button class="break-confirm flex items-center gap-2" onclick={setBreakConfirm}>
            {#if isBreakConfirmed}
                <svg width="27" height="27" viewBox="0 0 28 29" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path d="M11.9003 19.6332C11.7471 19.6334 11.5953 19.6032 11.4538 19.5446C11.3122 19.4859 11.1836 19.3999 11.0755 19.2914L7.34188 15.5584C7.12517 15.3392 7.00402 15.0432 7.00489 14.7349C7.00576 14.4267 7.12859 14.1313 7.34653 13.9134C7.56448 13.6954 7.85983 13.5726 8.16807 13.5717C8.4763 13.5708 8.77235 13.692 8.99155 13.9087L11.9002 16.8168L19.0085 9.70855C19.2278 9.49206 19.5238 9.3711 19.8319 9.37208C20.14 9.37305 20.4352 9.49589 20.6531 9.71376C20.8709 9.93163 20.9938 10.2269 20.9947 10.535C20.9957 10.8431 20.8747 11.1391 20.6582 11.3583L12.7251 19.2914C12.6169 19.3999 12.4884 19.4859 12.3468 19.5446C12.2053 19.6033 12.0535 19.6334 11.9003 19.6332V19.6332Z" fill="black"/>
                    <path d="M24.4999 2.83325H3.49992C3.1905 2.83325 2.89375 2.95617 2.67496 3.17496C2.45617 3.39375 2.33325 3.6905 2.33325 3.99992V24.9999C2.33325 25.3093 2.45617 25.6061 2.67496 25.8249C2.89375 26.0437 3.1905 26.1666 3.49992 26.1666H24.4999C24.8093 26.1666 25.1061 26.0437 25.3249 25.8249C25.5437 25.6061 25.6666 25.3093 25.6666 24.9999V3.99992C25.6666 3.6905 25.5437 3.39375 25.3249 3.17496C25.1061 2.95617 24.8093 2.83325 24.4999 2.83325V2.83325ZM20.6581 11.3582L12.725 19.2913C12.6167 19.3997 12.4881 19.4857 12.3466 19.5443C12.205 19.6029 12.0533 19.6331 11.9001 19.6331C11.7469 19.6331 11.5952 19.6029 11.4537 19.5443C11.3121 19.4857 11.1835 19.3997 11.0752 19.2913L7.34175 15.5584C7.12504 15.3392 7.00388 15.0431 7.00476 14.7349C7.00563 14.4267 7.12845 14.1313 7.3464 13.9133C7.56435 13.6954 7.8597 13.5725 8.16793 13.5717C8.47617 13.5708 8.77222 13.6919 8.99142 13.9086L11.9001 16.8167L19.0084 9.7085C19.2277 9.49201 19.5236 9.37105 19.8318 9.37203C20.1399 9.373 20.4351 9.49584 20.6529 9.71371C20.8708 9.93158 20.9936 10.2268 20.9946 10.5349C20.9956 10.843 20.8746 11.139 20.6581 11.3582V11.3582Z" fill="#66E2FF"/>
                </svg>
            {:else}
                <div class="w-[27px] h-[27px] bg-[#F0F0F0] rounded-[5px]"></div>
            {/if}
            <div class="font-semibold">I need to take a break, and I understand that I'll lose testing time.</div>
        </button>
        <div class="">If you're testing with a laptop, do not close it.</div>
        <div class="break-button-container flex w-full items-center justify-end gap-4 font-semibold">
            <button class="break-cancel-button border-solid border-black py-3 px-6 rounded-full hover:bg-[#e6e6e6]" onclick={() => setCurrentComponent('')}>Cancel</button>
            <button class="break-take-button border-solid border-black py-3 px-6 rounded-full bg-[#FFC800] hover:bg-[#deab02]" class:break-button-disabled={!isBreakConfirmed} disabled={!isBreakConfirmed} onclick={() => setCurrentComponent('breaking')}>Take Unscheduled Break</button>
        </div>
    </div>
</div>
{/if}

<!-- svelte-ignore a11y_no_static_element_interactions -->
<!-- svelte-ignore a11y_click_events_have_key_events -->

<!-- Exit -->
{#if isExitPopupOpen}
<div class="exit-overlay fixed inset-0 bg-black/80 z-50 flex justify-center items-center" onclick={(e) => e.stopPropagation()}>
    <div class="exit-popup bg-white flex flex-col gap-8 w-[70%] max-w-[1024px] px-[30px] py-[24px] rounded-[10px] font-['Inter']">
        <div class="text-2xl">Are You Sure You Want to Exit the Practice Arena? </div>
        <div>You will be taken back to Study Page. Your progress isn't saved. If you exit and come back, you'll start over.</div>
        <div class="exit-button-container flex gap-6 justify-end font-semibold">
            <button class="text-[#21D5FF] hover:text-[#4fc3f7]" onclick={() => setCurrentComponent('exit')}>Continue Practice Arena</button>
            <!-- <a href={data.slug != undefined ? `/study/practice-arena/${data.slug}` : "/"}><button class="exit-exit">Exit</button></a> -->
            <button class="bg-[#FFC800] border-solid border-black rounded-full py-3 px-6 hover:bg-[#deab02]">Exit</button>
        </div>
    </div>
</div>
{/if}

<!-- Breaking -->
{#if isBreaking}
<Breaking {setCurrentComponent} {minutes} {seconds} {isInModule} {handleSubmitModule}/>
{/if}

<!-- Reference -->
{#if isReferenceOpen}
    <Reference {setReference} />
{/if}

<!-- Calculator -->
{#if isCalculatorOpen}
    <Calculator {setCalculator} />
{/if}

<!-- Annotate -->
{#if isAnnotateOpen}
    <AnnotateEditPopup {setCurrentComponent} />
{/if}

<style>
    .tool-active:after {
        content: "";
        background-color: black;
        height: 2px;
        width: 100%;
        position: absolute;
        bottom: 0;
    }

    .break-button-disabled {
        background: #F0F0F0;
        border: #F0F0F0 solid 1.5px;
        color: #B2B2B2;
        cursor: not-allowed;
    }

    /* Width */
    ::-webkit-scrollbar {
        width: 8px;
        margin: 4px 0;
    }

    /* Track */
    ::-webkit-scrollbar-track {
        background: #DAF8FF;
        border-radius: 5px;
    }

    /* Handle */
    ::-webkit-scrollbar-thumb {
        background: #66E2FF;
        border-radius: 5px;
    }
</style>