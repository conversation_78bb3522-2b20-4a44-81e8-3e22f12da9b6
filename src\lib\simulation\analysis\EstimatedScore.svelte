<script lang="ts">
    import { H5 } from "$lib/ui";
    import ScoreChange from "./ScoreChange.svelte";
</script>

<div class="overview-container flex flex-col items-center justify-center p-4 border border-black bg-[var(--light-yellow)] rounded-lg font-['Inter'] h-full">
    <p class="text-2xl font-semibold">Your Estimated Score</p>
    <div class="score">1420</div> 
    <ScoreChange scoreChange={0}/>
    <p class="mt-2 text-sm">Since last simulation</p>

    <div class="score-components flex flex-col gap-3 mt-8">
        <div class="component flex items-center gap-3">
            <p class="component-score font-bold text-5xl text-[var(--sky-blue)]">700</p>
            <div class="flex flex-col">
                <p class="component-name font-semibold text-xl">Reading & Writing</p>
                <ScoreChange scoreChange={10}/>
            </div>
        </div>
        <div class="component flex items-center gap-3">
            <p class="component-score font-bold text-5xl text-[var(--rose)]">720</p>
            <div class="flex flex-col">
                <p class="component-name font-semibold text-xl">Math</p>
                <ScoreChange scoreChange={-10}/>
            </div>
        </div>
    </div>
</div>




<style>
    .overview-container {
        box-shadow: 0.25rem 0.25rem 0 var(--pitch-black);
    }

    .score {
        width: fit-content;
        font-size: 5.625rem;
        font-family: "Inter";
        font-weight: 800;
        -webkit-text-stroke: var(--pitch-black) 2px;
        paint-order: stroke fill;

        background: linear-gradient(90deg, var(--sky-blue) 0%, var(--rose) 100%);
        background-clip: text;
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        -webkit-filter: drop-shadow(0.25rem 0.25rem var(--pitch-black));
        filter: drop-shadow(0.25rem 0.25rem var(--pitch-black));
    }
</style>