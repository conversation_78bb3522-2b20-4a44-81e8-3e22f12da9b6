import { adminDB } from '$lib/server/admin.ts';
import { supabase } from "$lib/server";

export async function load({ locals }) {
    interface SimulationData {
        slug: number;
        title: string;
        progress: any;
    }

    // const { data: simulationsData, error: simulationsError } = await supabase
    //     .from('simulations')
    //     .select('slug, title')
    //     .order('slug', { ascending: true });

    // const simulations = simulationsData as SimulationData[];
    
    const simulations: SimulationData[] = [{
        slug: 1,
        title: "Simulation 1",
        progress: null
    }];
    
    await Promise.all(simulations.map(async (simulation) => {
        const simulationRef = adminDB.doc(`users/${locals.uid}/simulations/${simulation.slug}`);
        const simulationSnap = (await simulationRef.get()).data();
        simulation.progress = simulationSnap ?? null;
    }));

    return { simulationsData : simulations };
}
