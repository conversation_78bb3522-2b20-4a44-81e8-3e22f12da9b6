<script lang="ts">
	import { Button, H4, P3, P2 } from '$lib/ui';

	let { data } = $props();
    let windowWidth = $state(0);
    let isMobile = $derived(windowWidth < 920);
</script>

<svelte:head>
	<title>Simulations - DSAT16</title>
</svelte:head>

<svelte:window bind:innerWidth={windowWidth} />

<div class="simulations-wrapper bg-[var(--light-purple)] p-10 min-h-screen font-['Inter']">
	<div class="simulations flex flex-col gap-4 max-w-[1200px] mx-auto">
		<div class="title font-semibold text-3xl">
			Simulations
		</div>

        <P2>Full-length practice tests designed to closely resemble the style of the College Board's Digital SAT. These simulations are (probably) 16% more challenging than the six practice tests offered by Bluebook, making them ideal for students aiming to score 1400 and above. </P2>
        <P2>With tougher questions and a format that mirrors the actual test, these practice tests will help you sharpen your skills and boost your confidence for test day.</P2>

		<div class="simulations-cards grid grid-cols-1 md:grid-cols-2 gap-4">
			{#each data.simulationsData as simulation}
				<div class="simulations-card bg-[#DAF8FF] flex flex-col gap-4 p-4 border-solid border-black border-[1.5px] rounded-xl">
					<div class="flex justify-between items-center gap-4">
						<H4>Simulation 1</H4>
                        {#if !isMobile}
                            <div class="buttons-container flex gap-4">
                                {#if simulation.progress}
                                <a href="/study/analysis/{simulation.slug}">
                                    <Button isSecondary>
                                        Analysis
                                    </Button>
                                </a>
                                {/if}
                                <a href="./simulations/{simulation.slug}">
                                    <Button --button-bg-color="var(--sky-blue)">
                                        {simulation.progress ? "Retry" : "Start" }
                                    </Button>
                                </a>
                            </div>
                        {:else}
                            <P3>Mobile coming soon</P3>
                        {/if}
					</div>
					<div class="progress-bar border-solid border-black border-[1.5px] rounded-full px-2" style={` background: linear-gradient(90deg, #66E2FF ${(simulation.progress?.bestScore ?? 0) * 100 / 1600}%, #FFF 0%);`}>
						<div class="open-sans font-semibold text-lg">{simulation.progress?.bestScore ?? '?'}/1600</div>
					</div>
				</div>
			{/each}
			<div class="simulations-card bg-white flex flex-col gap-4 p-4 border-solid border-black border-[1.5px] rounded-xl">
				<H4>Coming Soon</H4>
				<div class="open-sans font-semibold text-lg">More Simulations will be added soon!</div>
			</div>
		</div>
	</div>
</div>


<style>
    .simulations-card {
        box-shadow: 0.25rem 0.25rem 0rem 0rem #000;
    }

    .open-sans {
        font-family: "Open Sans";
    }
</style>
