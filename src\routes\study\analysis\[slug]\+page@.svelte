<script lang="ts">
    import { run } from 'svelte/legacy';

	import Analysis from "$lib/analysis/Analysis.svelte";
    import NewAnalysis from "$lib/analysis/NewAnalysis.svelte";
    import { Top, Middle, Bottom, Review } from '$lib/simulation/walkthrough/index.js';
    
    /** @type {{data: any}} */
    let { data } = $props();


    // m is the index of the current module in the review section
    let m = $state(0);

    // i is the index of the current question
    let i = $state(data.practiceArena.modules[m].questions.length);


    function nextQuestion() {
        i++;
    }   

    function previousQuestion() {
        if (i === 0) return;
        i--;
    }

    function setQuestion(question) {
        i = question;
    }

    function toReview() {
        i = data.practiceArena.modules[m].questions.length;
    }


    // Analysis Mode
    let isInAnalysis = $state(false);

    function switchToAnalysis() {
        isInAnalysis = true;
    }

    function switchToResult() {
        isInAnalysis = false;
    }

    $effect(() => {
        if (m != null) i = data.practiceArena.modules[m].questions.length;
    });
    
    let isMath = $derived(["Algebra", "Data Analysis", "Geometry"].includes(data.practiceArena.modules[m].questions[i]?.questionType));
    let isSPR = $derived(!data.practiceArena.modules[m].questions[i]?.choices ?? false);
</script>

<svelte:head>
    <title>{data.practiceArena.title} - Analysis</title>
</svelte:head>

<div class="hide">
    <Top title={data.practiceArena.modules[m].title} {switchToAnalysis} {switchToResult} {isInAnalysis} hasAnalysis={true} />

    {#if isInAnalysis}
        <NewAnalysis studyPlan={data.studyPlan} weaknesses={data.weaknesses} strengths={data.strengths} student={data.student} scoreOfTypes={data.scoreOfTypes} scoreOfTopics={data.scoreOfTopics}/>
    {:else}
        {#if i > data.practiceArena.modules[m].questions.length - 1}
            <Review test={data.practiceArena.modules[m]} student={data.student} {setQuestion} bind:m />
        {:else}
            <Middle test={data.practiceArena.modules[m]} student={data.student} {i} {isMath} {isSPR} {m}/>
        {/if}
        
        {/if}
    <Bottom {isInAnalysis} test={data.practiceArena.modules[m]} student={data.student} {i} {nextQuestion} {setQuestion} {previousQuestion} {toReview} {m} />
</div>
<!-- 
<div class="show-wrapper">
    <div class="show">
        <div class="title">{data.practiceArena.title}</div>
        <div class="caution">Lưu ý</div>
        <div class="text">
            This feature is not supported on this device. To access {data.practiceArena.title}, please use a device with a bigger screen.
        </div>
    </div>
</div> -->

<!-- <style>
    .show {
        display: none;
    }

    @media only screen and (max-width: 960px) {
        .hide {
            display: none;
        }

        .show-wrapper {
            display: flex;
            justify-content: center;
            align-items: center;
            width: 100vw;
            height: 100vh;
        }

        .show {
            width: 75%;
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            gap: 14px;
            margin: 64px;
        }

        .title {
            color: #000;
            font-family: "Inter";
            font-size: 30px;
            font-style: normal;
            font-weight: 600;
            line-height: normal;
        }

        .caution {
            color: #000;
            font-family: "Inter";
            font-size: 24px;
            font-style: normal;
            font-weight: 600;
            line-height: normal;
        }

        .text {
            color: #000;
            font-family: "Open Sans";
            font-size: 16px;
            font-style: normal;
            font-weight: 400;
            line-height: 24px;
            text-align: center;
        }
    }
</style> -->