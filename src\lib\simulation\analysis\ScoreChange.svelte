<script lang="ts">
    const { scoreChange } = $props();

    const scoreDifference = Math.abs(scoreChange);
    const isPositive = scoreChange > 0;
    const noChange = scoreChange === 0;
</script>

<div class="score-change-container flex items-center justify-center gap-2 bg-[var(--light-aquamarine)] rounded-full border border-black px-2 w-36" class:bg-[var(--light-rose)]={!isPositive} class:bg-[#dbdbdb]={noChange}>
    {#if isPositive}
    <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
        <path d="M12.5293 7.96983C12.3887 7.82938 12.1981 7.75049 11.9993 7.75049C11.8006 7.75049 11.61 7.82938 11.4693 7.96983L4.46934 14.9698C4.36458 15.0747 4.29326 15.2083 4.26438 15.3537C4.23549 15.4991 4.25034 15.6498 4.30705 15.7868C4.36377 15.9237 4.45979 16.0408 4.58301 16.1232C4.70622 16.2056 4.8511 16.2497 4.99934 16.2498H18.9993C19.1476 16.2497 19.2925 16.2056 19.4157 16.1232C19.5389 16.0408 19.6349 15.9237 19.6916 15.7868C19.7483 15.6498 19.7632 15.4991 19.7343 15.3537C19.7054 15.2083 19.6341 15.0747 19.5293 14.9698L12.5293 7.96983Z" fill="#42FFB7"/>
    </svg>        
    {:else if !noChange}
    <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
        <path d="M11.4687 16.0307C11.6093 16.1711 11.8 16.25 11.9987 16.25C12.1975 16.25 12.3881 16.1711 12.5287 16.0307L19.5287 9.03066C19.6335 8.92577 19.7048 8.79219 19.7337 8.6468C19.7626 8.5014 19.7477 8.3507 19.691 8.21374C19.6343 8.07677 19.5383 7.95969 19.415 7.87727C19.2918 7.79485 19.1469 7.75079 18.9987 7.75066L4.99871 7.75066C4.85047 7.75079 4.70559 7.79485 4.58237 7.87727C4.45916 7.95969 4.36313 8.07677 4.30642 8.21374C4.24971 8.3507 4.23486 8.5014 4.26374 8.6468C4.29263 8.79219 4.36395 8.92577 4.46871 9.03066L11.4687 16.0307Z" fill="#FF66C4"/>
    </svg>    
    {:else}
    <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
        <path d="M12.5293 7.96983C12.3887 7.82938 12.1981 7.75049 11.9993 7.75049C11.8006 7.75049 11.61 7.82938 11.4693 7.96983L4.46934 14.9698C4.36458 15.0747 4.29326 15.2083 4.26438 15.3537C4.23549 15.4991 4.25034 15.6498 4.30705 15.7868C4.36377 15.9237 4.45979 16.0408 4.58301 16.1232C4.70622 16.2056 4.8511 16.2497 4.99934 16.2498H18.9993C19.1476 16.2497 19.2925 16.2056 19.4157 16.1232C19.5389 16.0408 19.6349 15.9237 19.6916 15.7868C19.7483 15.6498 19.7632 15.4991 19.7343 15.3537C19.7054 15.2083 19.6341 15.0747 19.5293 14.9698L12.5293 7.96983Z" fill="gray"/>
    </svg>
    {/if}
    <span class="text-[var(--aquamarine)] font-['Inter'] font-semibold text-sm" class:text-[var(--rose)]={!isPositive} class:text-[gray]={noChange}>{scoreDifference} points</span>
</div>