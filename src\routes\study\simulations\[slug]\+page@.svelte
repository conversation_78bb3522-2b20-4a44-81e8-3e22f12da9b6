<!-- svelte-ignore state_referenced_locally -->
<script lang='ts'>
    import { Top, Bottom, Middle, Review, Submitting } from "$lib/simulation/testInterface/index.js";
    import { addDoc, collection, serverTimestamp, doc, getDoc, setDoc } from 'firebase/firestore';
	import { db, user } from '$lib/firebase';
    import { goto } from '$app/navigation';
    import { currentAnnotate } from "$lib/annotate/annotateManager.js";
	import { P1, H2 } from "$lib/ui";

    interface StudentQuestionData {
        answer: number | string | null;
        marked: boolean;
        crossed: boolean[];
        index: number;
    }

    let { data } = $props();

    let isIntroOpen = $state(true);

    function startTest() {
        isIntroOpen = false;
    }

    let studentScores = {'RW1': 0, 'RW2': 0, 'M1': 0, 'M2': 0};
    let finalStudentAnswers = {'RW1': [], 'RW2': [], 'M1': [], 'M2': []};

    let currentQuestionIndex = $state(0);
    let currentModuleIndex = $state(0);

    let currentModule = $derived(data.modules[currentModuleIndex]);

    let studentQuestionsData: StudentQuestionData[] = $state(Array(currentModule.questions.length).fill({ answer: null, marked: false, crossed: Array(4).fill(false), index: null }));

    studentQuestionsData.forEach((question, index) => question.index = index);

    let currentQuestion = $derived(currentModule.questions[currentQuestionIndex]);
    let currentStudentData = $derived(studentQuestionsData[currentQuestionIndex]);

    let moduleTitle = $derived(currentModule.title);

    let isMath = $derived(['Algebra', 'Data Analysis', 'Geometry'].includes(currentModule?.questions[currentQuestionIndex]?.topic));

    function setQuestion(index: number) {
        currentQuestionIndex = index;
    }

    function setMarked() {
        studentQuestionsData[currentQuestionIndex].marked = !studentQuestionsData[currentQuestionIndex].marked;
    }

    function setCrossed(choice: number) {
        if (studentQuestionsData[currentQuestionIndex].answer === choice) setAnswer(null);

        studentQuestionsData[currentQuestionIndex].crossed[choice] = !studentQuestionsData[currentQuestionIndex].crossed[choice];
    }

    function setAnswer(answer: number | string | null) {
        if (typeof answer === 'number' && studentQuestionsData[currentQuestionIndex].crossed[answer]) {
            studentQuestionsData[currentQuestionIndex].crossed[answer] = false;
        }
        studentQuestionsData[currentQuestionIndex].answer = answer;
    }

    function toReview() {
        currentQuestionIndex = currentModule.questions.length;
        setCurrentComponent('');
    }

    let isReview = $derived(currentQuestionIndex === currentModule.questions.length);


    let isCalculatorOpen = $state(false);

    function setCalculator() {
        isCalculatorOpen = !isCalculatorOpen;
    }

    let isInModule = $state(true);

    function nextModule() {
        isModuleLoading = true;
        isInModule = true;
        currentModuleIndex++;
        studentQuestionsData = Array(currentModule.questions.length).fill({ answer: null, marked: false, crossed: Array(4).fill(false)});
        studentQuestionsData.forEach((question, index) => question.index = index);
        currentQuestionIndex = 0;
        isModuleLoading = false;
    }

    async function handleSubmitModule() {
        if (!isInModule) {
            nextModule();
            return;
        }
        
        // Helper function to check correct answer
        function isCorrect(answer: number | string | null, correct: number | string): boolean {
            if (answer === null) return false;
            if (typeof correct === 'number') return answer === correct;
            try {
                const correctValue = Function("return " + correct)();
                const answerValue = Function("return " + answer)();
                
                const posibleValue = [Math.round(correctValue * 10000) / 10000, Math.floor(correctValue * 10000) / 10000, Math.round(correctValue * 1000) / 1000, Math.floor(correctValue * 1000) / 1000]; 
                return (answer as string).includes('/') ? answerValue === correctValue : posibleValue.includes(answerValue);
            } catch {
                return false;
            }
        }

        isSubmitting = true;

        let correctAnswers = currentModule.questions.map((question) => question.correctAnswer);

        finalStudentAnswers[currentModule.id] = studentQuestionsData.map((question, index) => {
            return {
                answer: question.answer,
                isCorrect: isCorrect(question.answer, correctAnswers[index]),
            };
        });

        studentScores[currentModule.id] = finalStudentAnswers[currentModule.id].reduce((acc, cur) => cur.isCorrect ? acc + 1 : acc, 0);

        isSubmitting = false;

        if (currentModuleIndex === 1) {
            isInModule = false;
            return;
        }

        if (currentModuleIndex === 3) {
            await submitSimulation();
            return;
        }

        nextModule();
    }

    async function submitSimulation(): Promise<void> {
        isSubmitting = true;

    	const VERBAL_SCORES: number[] = [
            200, 200, 200, 200, 200, 210, 230, 240, 250, 260,  // 0-9
            290, 320, 330, 340, 350, 360, 370, 380, 390, 400,  // 10-19
            410, 430, 440, 440, 450, 460, 480, 480, 490, 500,  // 20-29
            500, 510, 520, 530, 550, 560, 570, 580, 590, 600,  // 30-39
            620, 630, 640, 650, 670, 680, 690, 700, 710, 720,  // 40-49
            730, 750, 770, 790, 800                            // 50-54
        ];

    	const MATH_SCORES: number[] = [
            200, 200, 200, 200, 220, 240, 260, 270, 290, 300,  // 0-9
            320, 330, 350, 360, 380, 390, 410, 420, 440, 450,  // 10-19
            470, 480, 500, 510, 530, 540, 560, 570, 590, 600,  // 20-29
            620, 630, 650, 660, 680, 690, 710, 720, 740, 750,  // 30-39
            760, 770, 780, 790, 800                            // 40-44
        ];

        const predictedVerbalScore: number = VERBAL_SCORES[studentScores.RW1 + studentScores.RW2];
		const predictedMathScore: number = MATH_SCORES[studentScores.M1 + studentScores.M2];
        const predictedTotalScore: number = predictedVerbalScore + predictedMathScore;

        try {
            const simulationRef = doc(db, 'users', data.uid, 'simulations', String(data.slug));

            const simulationAttempt = await addDoc(
                collection(simulationRef, 'attempts'),
                {
                    answers: finalStudentAnswers,
                    scores: studentScores,
                    predictedVerbalScore,
                    predictedMathScore,
                    predictedTotalScore: predictedTotalScore,
                    submitTime: serverTimestamp(),
                }
            );

            let simulationData = (await getDoc(simulationRef)).data() ?? { attempts: 0, bestScore: 0, bestAttempt: null };

            await setDoc(simulationRef, {
                attempts: simulationData.attempts + 1,
                bestScore: Math.max(simulationData.bestScore, predictedTotalScore),
                bestAttempt: simulationData.bestScore < predictedTotalScore ? simulationAttempt.id : simulationData.bestAttempt,
            });
        } catch (error) {
            console.error("Error adding document: ", error);
        }

        isSubmitting = false;

        // goto('/test/test');
    }


    // Current active component
    type ComponentState = "direction" | "more" | "break" | "breaking" | "exit" | "annoTip" | "annotate" | "";

    let currentComponent: ComponentState = $state("") as ComponentState;

    function setCurrentComponent(component: ComponentState) {
        if (currentComponent === component) currentComponent = "";
        else currentComponent = component;
    }

    // Close popups when clicking outside
    function handleClickOutside(e: MouseEvent) {
        // Skip if target is mark
        if ((e.target as HTMLElement).tagName === "MARK") return;
        currentComponent = "";
    }


    // Annotate
    let annotateManager = $state(null);
    let currentAnnotateManager = $derived(annotateManager?.[currentQuestionIndex] ?? null);
    let currentModuleLength = $derived(currentModule.questions.length);

    $effect(() => {
        if ($currentAnnotate) currentComponent = "annotate";
    })

    let isModuleLoading = $state(false);
    let isSubmitting = $state(false);


    // Window properties
    let windowWidth = $state(1000);

    // Disable copy/paste
    function handleKeydown(e: KeyboardEvent) {
        if ((e.ctrlKey || e.metaKey) && ["c", "x"].includes(e.key.toLowerCase())) {
            e.preventDefault();
            alert("Copying is not allowed while testing.");

            // Reset selection
            window.getSelection()?.empty();
        }
    }
</script>

<svelte:head>
    <title>Simulation {data.slug} - DSAT16</title>
</svelte:head>


<svelte:window bind:innerWidth={windowWidth} oncontextmenu={(e) => e.preventDefault()} onkeydown={handleKeydown}/>
<svelte:document onclick={handleClickOutside}/>


{#if !isModuleLoading && !isSubmitting}
<div class="simulation-container fixed inset-0 flex flex-col">
    {#if !isIntroOpen}
        <div class="top-container h-[90px]">
            <Top {currentModuleIndex} {moduleTitle} {isMath} {isCalculatorOpen} {setCalculator} {isReview} {handleSubmitModule} {isInModule} {currentComponent} {setCurrentComponent} {currentAnnotateManager}/>
        </div>
    {/if}

    <div class="middle-container flex-1 overflow-y-auto flex flex-col items-center">
        {#if isIntroOpen}
            <div></div>
        {:else}
            {#if isReview}
                <Review {moduleTitle} {studentQuestionsData} {setQuestion}/>
            {:else}
                <Middle {currentQuestion} {currentStudentData} {setAnswer} {setMarked} {setCrossed} {isMath} {isCalculatorOpen} bind:annotateManager {currentModuleIndex} {currentModuleLength} {currentQuestionIndex}/>
            {/if}
        {/if}
    </div>

    <div class="bottom-container h-[90px]">
        <Bottom {isIntroOpen} {startTest} {moduleTitle} {currentQuestionIndex} {studentQuestionsData}  {setQuestion} {toReview} {isReview} {handleSubmitModule} {currentComponent} {setCurrentComponent}/>
    </div>
</div>
{:else}
    <Submitting title={isSubmitting ? data.modules[currentModuleIndex].title : data.modules[currentModuleIndex+1].title} {isModuleLoading}/>
{/if}

{#if windowWidth < 920}
<div class="fixed inset-0 flex justify-center items-center bg-white z-[100]">
	<div class="w-3/4 max-w-[600px] flex flex-col items-center justify-center gap-4 text-center">
		<H2>Caution</H2>
		<P1>
			This feature is not supported on this device. To access {data.title}, please switch to a device with a larger screen.
        </P1>
	</div>
</div>
{/if}

