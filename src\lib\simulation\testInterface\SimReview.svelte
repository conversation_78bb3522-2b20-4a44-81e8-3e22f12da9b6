<!-- 
    @component
    ## SimReview
    A component to display the review section of the simulation test. It shows the list of questions and their status (answered, unanswered, marked, etc.).
-->

<script lang="ts">
    let {
        moduleTitle,
        studentQuestionsData,
        setQuestion
    } = $props();
</script>

<div class="middle w-full overflow-y-auto">
    <div class="review-container max-w-[1100px] py-[50px] flex flex-col gap-10 items-center w-full font-['Inter'] m-auto">
        <div class="text-4xl">Check your Work</div>
        <div class="flex flex-col gap-4">
            <p>On test day, you won't be able to move on to the next module until time expires.</p>
            <p>For these practice questions, you can click <span class="font-bold">Next</span> when you're ready to submit.</p>
        </div>
        <div class="review-nav flex flex-col p-10 gap-6 rounded-lg w-4/5">
            <div class="review-nav-head flex justify-between w-full">
                <div class="title text-xl font-semibold">{moduleTitle}</div>
                <div class="icons flex gap-4">
                    <div class="flex gap-2 items-center">
                        <div class="unanswered-icon w-6 h-6 border border-dashed border-black"></div>
                        <div class="text-[15px]">Unanswered</div>
                    </div>
                    <div class="flex gap-1 items-center">
                        <svg xmlns="http://www.w3.org/2000/svg" width="22" height="22" viewBox="0 0 22 22" fill="none">
                            <path d="M14.6663 1.83301H7.33301C5.77467 1.83301 4.58301 3.02467 4.58301 4.58301V19.2497C4.58301 19.433 4.58301 19.5247 4.67467 19.708C4.94967 20.1663 5.49967 20.258 5.95801 20.0747L10.9997 17.1413L16.0413 20.0747C16.2247 20.1663 16.3163 20.1663 16.4997 20.1663C17.0497 20.1663 17.4163 19.7997 17.4163 19.2497V4.58301C17.4163 3.02467 16.2247 1.83301 14.6663 1.83301Z" fill="#FF66C4"/>
                        </svg>
                        <div class="text-[15px]">For Review</div>
                    </div>
                </div>
            </div>
            <div class="separate-line w-full h-[1px] bg-[#B2B2B2]"></div>
            <div class="nav-answer-list w-full grid grid-cols-[repeat(10,minmax(0,1fr))] gap-7">
                {#each studentQuestionsData as question, index}
                    <button class="border border-dashed border-black w-11 h-11 flex-shrink-0 font-semibold text-2xl relative hover:bg-[#e6e6e6]" class:answered-box={question.answer !== null} onclick={() => setQuestion(index)}>
                        {index + 1}
                        {#if question.marked}
                            <svg class="marked-icon absolute right-[-20%] top-[-20%] z-10" xmlns="http://www.w3.org/2000/svg" width="15" height="15" viewBox="0 0 15 15" fill="none">
                                <path d="M10 1.25H5C3.9375 1.25 3.125 2.0625 3.125 3.125V13.125C3.125 13.25 3.125 13.3125 3.1875 13.4375C3.375 13.75 3.75 13.8125 4.0625 13.6875L7.5 11.6875L10.9375 13.6875C11.0625 13.75 11.125 13.75 11.25 13.75C11.625 13.75 11.875 13.5 11.875 13.125V3.125C11.875 2.0625 11.0625 1.25 10 1.25Z" fill="#FF66C4"/>
                            </svg>
                        {/if}
                    </button>    
                {/each}
            </div>
        </div>
    </div>
</div>

<style>
    .review-nav {
        box-shadow: 0px 0px 7px 0px rgba(0, 0, 0, 0.25);
    }

    .answered-box {
        border: none;
        text-decoration: underline;
        background-color: #66E2FF;
    }

    .answered-box:hover {
        background-color: #07cbf7;
    }

    /* Width */
    ::-webkit-scrollbar {
        width: 8px;
        margin: 4px 0;
    }

    /* Track */
    ::-webkit-scrollbar-track {
        background: #DAF8FF;
        border-radius: 5px;
    }

    /* Handle */
    ::-webkit-scrollbar-thumb {
        background: #66E2FF;
        border-radius: 5px;
    }
</style>