<!-- 
    @component
    ## WalkReview
    This component is used to display the review of the test. It shows the number of questions answered correctly, incorrectly and unanswered. It also shows the list of questions with the correct, incorrect and unanswered status. The user can click on the question to navigate to the question.
-->

<script lang="ts">
    let {
        moduleTitle,
        currentModuleIndex,
        currentModuleAnswers,
        setQuestion,
        setModule
    } = $props();
</script>

<div class="middle w-full overflow-y-auto">
    <div class="review-container max-w-[1100px] py-[50px] flex flex-col gap-10 items-center w-full font-['Inter'] m-auto">
        <div class="text-4xl">You have submitted {moduleTitle}</div>
        <div class="flex flex-col gap-4 text-center">
            <p>You got <span class="font-semibold">0/98</span> questions right, with an estimated score of <span class="font-semibold">400/1600</span>. Well Done!</p>
            <p class="font-semibold underline">You can check your answers and detailed solutions by clicking each question below.</p>
        </div>

        <div class="review-nav flex gap-4 relative">
            <button aria-label="Previous module" class="arrow-left absolute top-1/2 -translate-y-1/2 -left-[8%]" class:hidden={currentModuleIndex === 0} onclick={() => setModule(currentModuleIndex - 1)}>
                <svg width="43" height="30" viewBox="0 0 43 73" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path fill-rule="evenodd" clip-rule="evenodd" d="M2.44272 40.529L34.4991 72.5853L42.5117 64.5726L14.4617 36.5226L42.5117 8.47263L34.4991 0.459961L2.44272 32.5163C1.38038 33.5789 0.783596 35.02 0.783596 36.5226C0.783596 38.0252 1.38038 39.4663 2.44272 40.529Z" fill="#66E2FF"/>
                </svg>                        
            </button>
            {#each ["R&W - Module 1", "R&W - Module 2", "Math - Module 1", "Math - Module 2"] as module, index}
                <button class="border-solid border-black py-3 px-6 rounded-full hover:bg-[#07cbf7] font-semibold" class:bg-[#66E2FF]={index === currentModuleIndex} onclick={() => setModule(index)}>{module}</button>
            {/each}
            <button aria-label="Next module" class="arrow-right absolute top-1/2 -translate-y-1/2 -right-[8%]" class:hidden={currentModuleIndex === 3} onclick={() => setModule(currentModuleIndex + 1)}>
                <svg width="43" height="30" viewBox="0 0 43 73" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path fill-rule="evenodd" clip-rule="evenodd" d="M40.5573 40.529L8.50095 72.5853L0.488281 64.5726L28.5383 36.5226L0.488281 8.47263L8.50095 0.459961L40.5573 32.5163C41.6196 33.5789 42.2164 35.02 42.2164 36.5226C42.2164 38.0252 41.6196 39.4663 40.5573 40.529Z" fill="#66E2FF"/>
                </svg>
            </button>
        </div>

        <div class="review-box-container flex flex-col p-10 gap-6 rounded-lg w-4/5 relative">
            <div class="review-box-head flex justify-between w-full">
                <div class="title text-xl font-semibold">{moduleTitle}</div>
                <div class="icons flex gap-4">
                    <div class="flex gap-2 items-center">
                        <div class="unanswered-icon w-6 h-6 border border-dashed border-black bg-[#D1FFEE]"></div>
                        <div class="text-[15px]">Correct</div>
                    </div>
                    <div class="flex gap-2 items-center">
                        <div class="unanswered-icon w-6 h-6 border border-dashed border-black bg-[#FFDAF1]"></div>
                        <div class="text-[15px]">Incorrect</div>
                    </div>
                    <div class="flex gap-2 items-center">
                        <div class="unanswered-icon w-6 h-6 border border-dashed border-black"></div>
                        <div class="text-[15px]">Unanswered</div>
                    </div>
                </div>
            </div>
            <div class="separate-line w-full h-[1px] bg-[#B2B2B2]"></div>
            <div class="review-box-answerlist w-full grid grid-cols-[repeat(10,minmax(0,1fr))] gap-7">
                {#each currentModuleAnswers as answer, index}
                    <button class="border border-dashed border-black w-11 h-11 flex-shrink-0 font-semibold text-2xl hover:bg-[#e6e6e6]" class:bg-[#D1FFEE]={answer.isCorrect} class:bg-[#FFDAF1]={answer.answer !== null && !answer.isCorrect} onclick={() => setQuestion(index)}>
                        {index + 1}
                    </button>
                {/each}
            </div>
        </div>
    </div>
</div>

<style>
    .review-box-container {
        box-shadow: 0px 0px 7px 0px rgba(0, 0, 0, 0.25);
    }

    /* Width */
    ::-webkit-scrollbar {
        width: 8px;
        height: 8px;
        margin: 4px 0;
    }

    /* Track */
    ::-webkit-scrollbar-track {
        background: #DAF8FF;
        border-radius: 5px;
    }

    /* Handle */
    ::-webkit-scrollbar-thumb {
        background: #66E2FF;
        border-radius: 5px;
    }
</style>