<!-- 
    @component
    ## SimBottom
    A component to display the bottom part of the simulation test.
-->

<script lang="ts">
    let { 
        isIntroOpen,
        startTest,
        moduleTitle,
        currentQuestionIndex,
        studentQuestionsData,
        setQuestion, 
        toReview,
        isReview,
        handleSubmitModule,
        currentComponent, 
        setCurrentComponent
    } = $props();
    
    let isNavBoxOpen = $derived(currentComponent === 'navBox');

    function handleBack() {
        setQuestion(currentQuestionIndex - 1);
    }

    function handleNext() {
        setQuestion(currentQuestionIndex + 1);
    }

</script>

<!-- Bottom -->
<div class="bottom bg-white h-[90px] flex justify-between items-center p-4 px-[64px] border-t-[3px] border-[#505050] select-none font-['Inter'] font-semibold">
    <div class="logo">
        <svg xmlns="http://www.w3.org/2000/svg" width="119" height="24" viewBox="0 0 119 24" fill="none">
            <path d="M94.8819 0.319392V23.6806H89.247V5.58935H89.1101L83.8857 8.78327V3.90114L89.6462 0.319392H94.8819Z" fill="url(#paint0_linear_924_1619)"/>
            <path d="M109.411 24C108.134 24 106.91 23.7947 105.738 23.384C104.567 22.9658 103.525 22.3042 102.613 21.3992C101.7 20.4867 100.982 19.2928 100.457 17.8175C99.9323 16.3346 99.6738 14.5285 99.6814 12.3992C99.689 10.4753 99.9247 8.74905 100.389 7.22053C100.852 5.68441 101.514 4.38023 102.373 3.30798C103.24 2.23574 104.275 1.41825 105.476 0.855513C106.685 0.285171 108.035 0 109.525 0C111.16 0 112.602 0.319392 113.849 0.958175C115.103 1.58935 116.107 2.44106 116.86 3.51331C117.613 4.57795 118.058 5.76426 118.195 7.07224H112.64C112.472 6.3346 112.103 5.77566 111.533 5.39544C110.97 5.0076 110.301 4.81369 109.525 4.81369C108.096 4.81369 107.027 5.43346 106.32 6.673C105.621 7.91255 105.263 9.57034 105.248 11.6464H105.396C105.716 10.9468 106.176 10.346 106.776 9.84411C107.377 9.34221 108.065 8.95817 108.841 8.69201C109.624 8.41825 110.453 8.28137 111.328 8.28137C112.727 8.28137 113.963 8.60456 115.035 9.25095C116.107 9.89734 116.948 10.7833 117.556 11.9087C118.164 13.0266 118.465 14.308 118.457 15.7529C118.465 17.3802 118.084 18.8175 117.316 20.0646C116.548 21.3042 115.484 22.27 114.122 22.962C112.769 23.654 111.198 24 109.411 24ZM109.377 19.6654C110.069 19.6654 110.689 19.5019 111.237 19.1749C111.784 18.8479 112.214 18.403 112.525 17.8403C112.837 17.2776 112.989 16.6426 112.982 15.9354C112.989 15.2205 112.837 14.5856 112.525 14.0304C112.221 13.4753 111.795 13.0342 111.248 12.7072C110.708 12.3802 110.088 12.2167 109.389 12.2167C108.879 12.2167 108.404 12.3118 107.963 12.5019C107.522 12.692 107.138 12.9582 106.811 13.3004C106.491 13.635 106.24 14.0304 106.058 14.4867C105.875 14.9354 105.78 15.4221 105.773 15.9468C105.78 16.6388 105.94 17.2662 106.252 17.8289C106.563 18.3916 106.989 18.8403 107.529 19.1749C108.069 19.5019 108.685 19.6654 109.377 19.6654Z" fill="url(#paint1_linear_924_1619)"/>
            <path d="M8.27519 23.1451H0V0.757256H8.26426C10.5453 0.757256 12.5093 1.20545 14.1564 2.10184C15.8107 2.99094 17.086 4.27357 17.9824 5.94975C18.8788 7.61863 19.327 9.61546 19.327 11.9402C19.327 14.2723 18.8788 16.2764 17.9824 17.9526C17.0933 19.6288 15.8216 20.915 14.1673 21.8114C12.513 22.7005 10.549 23.1451 8.27519 23.1451ZM5.41112 18.532H8.06749C9.32098 18.532 10.3813 18.3206 11.2486 17.8979C12.1231 17.468 12.7826 16.772 13.2272 15.81C13.679 14.8407 13.9049 13.5508 13.9049 11.9402C13.9049 10.3297 13.679 9.04702 13.2272 8.09233C12.7753 7.13035 12.1085 6.43802 11.2267 6.01534C10.3522 5.58536 9.27361 5.37037 7.99097 5.37037H5.41112V18.532Z" fill="#303030"/>
            <path d="M34.5875 7.46923C34.5146 6.66758 34.1903 6.04449 33.6145 5.59994C33.0461 5.1481 32.2335 4.92218 31.1768 4.92218C30.4772 4.92218 29.8942 5.01328 29.4278 5.19547C28.9613 5.37766 28.6115 5.62909 28.3783 5.94975C28.1451 6.26312 28.0249 6.62386 28.0176 7.03197C28.003 7.3672 28.0686 7.66236 28.2144 7.91743C28.3674 8.1725 28.586 8.39842 28.8702 8.59518C29.1618 8.78466 29.5116 8.95228 29.9197 9.09804C30.3278 9.24379 30.7869 9.37132 31.2971 9.48064L33.221 9.9179C34.3287 10.1584 35.3053 10.4791 36.1507 10.8799C37.0033 11.2807 37.7175 11.758 38.2932 12.3119C38.8763 12.8658 39.3172 13.5035 39.616 14.2249C39.9148 14.9464 40.0678 15.7554 40.0751 16.6517C40.0678 18.0656 39.7107 19.279 39.0038 20.292C38.2969 21.3049 37.2803 22.0811 35.9539 22.6204C34.6348 23.1597 33.0425 23.4293 31.1768 23.4293C29.3039 23.4293 27.6714 23.1487 26.2795 22.5876C24.8875 22.0264 23.8053 21.1738 23.0328 20.0296C22.2603 18.8854 21.8631 17.4388 21.8413 15.6898H27.0228C27.0665 16.4112 27.2597 17.0125 27.6022 17.4935C27.9447 17.9745 28.4148 18.3388 29.0124 18.5866C29.6172 18.8344 30.3169 18.9583 31.1112 18.9583C31.84 18.9583 32.4594 18.8599 32.9696 18.6631C33.487 18.4664 33.8842 18.1931 34.1611 17.8433C34.4381 17.4935 34.5802 17.0926 34.5875 16.6408C34.5802 16.2181 34.449 15.8574 34.1939 15.5586C33.9388 15.2525 33.5453 14.9901 33.0133 14.7715C32.4886 14.5456 31.8181 14.3379 31.0019 14.1484L28.6625 13.6018C26.724 13.1573 25.1972 12.4394 24.0822 11.4483C22.9672 10.4499 22.4133 9.10168 22.4206 7.40364C22.4133 6.01898 22.785 4.80558 23.5356 3.76343C24.2863 2.72129 25.3248 1.90871 26.6511 1.3257C27.9775 0.742681 29.4897 0.451172 31.1877 0.451172C32.9222 0.451172 34.4271 0.746324 35.7025 1.33663C36.9851 1.91964 37.9799 2.73951 38.6868 3.79623C39.3937 4.85295 39.7544 6.07728 39.769 7.46923H34.5875Z" fill="#303030"/>
            <path d="M46.9592 23.1451H41.1437L48.6974 0.757256H55.9013L63.455 23.1451H57.6394L52.3813 6.39794H52.2064L46.9592 23.1451ZM46.1831 14.3343H58.339V18.4445H46.1831V14.3343Z" fill="#303030"/>
            <path d="M62.3372 5.15174V0.757256H81.2597V5.15174H74.4712V23.1451H69.1366V5.15174H62.3372Z" fill="#303030"/>
            <defs>
              <linearGradient id="paint0_linear_924_1619" x1="120.1" y1="-4.69355" x2="93.4672" y2="-8.61015" gradientUnits="userSpaceOnUse">
                <stop stop-color="#66E2FF"/>
                <stop offset="1" stop-color="#FF66C4"/>
              </linearGradient>
              <linearGradient id="paint1_linear_924_1619" x1="120.1" y1="-4.69355" x2="93.4672" y2="-8.61015" gradientUnits="userSpaceOnUse">
                <stop stop-color="#66E2FF"/>
                <stop offset="1" stop-color="#FF66C4"/>
              </linearGradient>
            </defs>
        </svg>
    </div>

    {#if isIntroOpen}
        <!-- Nav button -->
        <div class="nav-button-container">
            <button class="nav-button bg-[#66E2FF] border-solid border-black py-3 px-6 rounded-full hover:bg-[#07cbf7]" onclick={startTest}>Start The Test</button>
        </div>
    {:else}
        <!-- Nav button -->
        {#if !isReview}
            <button class="nav-toggle absolute left-1/2 -translate-x-1/2 bg-black flex justify-center items-center gap-1 pl-4 pr-1 py-2 rounded-lg text-white hover:opacity-70" onclick={(e) => {
                e.stopPropagation(); 
                setCurrentComponent('navBox');
            }}>
                <div class="nav-text">Question {currentQuestionIndex + 1} of {studentQuestionsData.length}</div>
                <svg class:rotate-180={isNavBoxOpen} xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none">
                    <path d="M11.29 8.46026L5.64004 14.1203C5.54631 14.2132 5.47191 14.3238 5.42115 14.4457C5.37038 14.5675 5.34424 14.6983 5.34424 14.8303C5.34424 14.9623 5.37038 15.093 5.42115 15.2148C5.47191 15.3367 5.54631 15.4473 5.64004 15.5403C5.8274 15.7265 6.08085 15.8311 6.34504 15.8311C6.60922 15.8311 6.86267 15.7265 7.05004 15.5403L12.05 10.5903L17 15.5403C17.1874 15.7265 17.4409 15.8311 17.705 15.8311C17.9692 15.8311 18.2227 15.7265 18.41 15.5403C18.5045 15.4476 18.5797 15.3372 18.6312 15.2153C18.6827 15.0935 18.7095 14.9626 18.71 14.8303C18.7095 14.698 18.6827 14.5671 18.6312 14.4452C18.5797 14.3233 18.5045 14.2129 18.41 14.1203L12.76 8.46026C12.6664 8.35876 12.5527 8.27775 12.4262 8.22234C12.2997 8.16693 12.1631 8.13833 12.025 8.13833C11.8869 8.13833 11.7503 8.16693 11.6238 8.22234C11.4973 8.27775 11.3837 8.35876 11.29 8.46026Z" fill="white"/>
                </svg>
            </button>
        {/if}
        
        <!-- Back & Next button -->
        <div class="nav-button-container flex gap-4 items-center">
            {#if currentQuestionIndex !== 0}
                <button class="nav-button bg-[#66E2FF] border-solid border-black py-3 px-6 rounded-full hover:bg-[#07cbf7]" onclick={handleBack}>Back</button>
            {/if}

            <button class="nav-button bg-[#66E2FF] border-solid border-black py-3 px-6 rounded-full hover:bg-[#07cbf7]" onclick={currentQuestionIndex == studentQuestionsData.length ? handleSubmitModule : handleNext}>Next</button>
        </div>
    {/if}
</div>


<!-- svelte-ignore a11y_no_static_element_interactions -->
<!-- svelte-ignore a11y_click_events_have_key_events -->

<!-- Navigation Box -->
{#if isNavBoxOpen}
<div class="nav-box w-[575px] bg-white fixed bottom-[96px] left-1/2 -translate-x-1/2 flex flex-col gap-3 p-6 items-center rounded-lg font-['Inter'] shadow-xl" onclick={(e) => e.stopPropagation()}>
    <div class="nav-title text-xl font-semibold">{moduleTitle}</div>
    <div class="separate-line w-full h-[1.5px] bg-[#E0E0E0]"></div>
    <div class="nav-caption-container flex flex-row items-center justify-center gap-6 w-full text-sm">
        <div class="flex gap-1 items-center">
            <svg xmlns="http://www.w3.org/2000/svg" width="23" height="23" viewBox="0 0 23 23" fill="none">
                <path d="M11.4998 2.33301C9.55492 2.33301 7.68965 3.10562 6.31439 4.48089C4.93912 5.85616 4.1665 7.72142 4.1665 9.66634C4.1665 14.6163 10.629 20.208 10.904 20.4463C11.07 20.5884 11.2813 20.6664 11.4998 20.6664C11.7183 20.6664 11.9296 20.5884 12.0957 20.4463C12.4165 20.208 18.8332 14.6163 18.8332 9.66634C18.8332 7.72142 18.0606 5.85616 16.6853 4.48089C15.31 3.10562 13.4448 2.33301 11.4998 2.33301ZM11.4998 18.5122C9.54734 16.6788 5.99984 12.728 5.99984 9.66634C5.99984 8.20765 6.5793 6.8087 7.61075 5.77725C8.6422 4.7458 10.0411 4.16634 11.4998 4.16634C12.9585 4.16634 14.3575 4.7458 15.3889 5.77725C16.4204 6.8087 16.9998 8.20765 16.9998 9.66634C16.9998 12.728 13.4523 16.688 11.4998 18.5122ZM11.4998 5.99967C10.7746 5.99967 10.0657 6.21472 9.46275 6.61762C8.85977 7.02052 8.3898 7.59317 8.11228 8.26317C7.83476 8.93316 7.76215 9.67041 7.90362 10.3817C8.0451 11.0929 8.39432 11.7463 8.90711 12.2591C9.4199 12.7719 10.0732 13.1211 10.7845 13.2626C11.4958 13.404 12.233 13.3314 12.903 13.0539C13.573 12.7764 14.1457 12.3064 14.5486 11.7034C14.9515 11.1005 15.1665 10.3915 15.1665 9.66634C15.1665 8.69388 14.7802 7.76125 14.0926 7.07362C13.4049 6.38598 12.4723 5.99967 11.4998 5.99967ZM11.4998 11.4997C11.1372 11.4997 10.7828 11.3921 10.4813 11.1907C10.1798 10.9893 9.94482 10.7029 9.80606 10.3679C9.6673 10.0329 9.63099 9.66431 9.70173 9.30867C9.77247 8.95304 9.94708 8.62637 10.2035 8.36998C10.4599 8.11358 10.7865 7.93897 11.1422 7.86823C11.4978 7.79749 11.8664 7.8338 12.2014 7.97256C12.5364 8.11132 12.8227 8.34631 13.0242 8.6478C13.2256 8.94929 13.3332 9.30374 13.3332 9.66634C13.3332 10.1526 13.14 10.6189 12.7962 10.9627C12.4524 11.3065 11.9861 11.4997 11.4998 11.4997Z" fill="black"/>
            </svg>
            <span>Current</span>
        </div>
        <div class="flex gap-2 items-center translate-x-2">
            <div class="unanswered-icon w-6 h-6 border-dashed border-black border"></div>
            <span>Unanswered</span>
        </div>
        <div class="flex gap-1 items-center">
            <svg xmlns="http://www.w3.org/2000/svg" width="23" height="23" viewBox="0 0 23 23" fill="none">
                <path d="M15.1668 2.33301H7.8335C6.27516 2.33301 5.0835 3.52467 5.0835 5.08301V19.7497C5.0835 19.933 5.0835 20.0247 5.17516 20.208C5.45016 20.6663 6.00016 20.758 6.4585 20.5747L11.5002 17.6413L16.5418 20.5747C16.7252 20.6663 16.8168 20.6663 17.0002 20.6663C17.5502 20.6663 17.9168 20.2997 17.9168 19.7497V5.08301C17.9168 3.52467 16.7252 2.33301 15.1668 2.33301Z" fill="#FF66C4"/>
            </svg>
            <span>For Review</span>
        </div>
    </div>
    <div class="separate-line w-full h-[1.5px] bg-[#E0E0E0]"></div>
    <div class="nav-answer-list w-full grid grid-cols-[repeat(10,minmax(0,1fr))] mb-2 gap-3">
        {#each studentQuestionsData as question, index}
            <div class="answer-box-container relative max-w-9 mt-4">
                {#if index === currentQuestionIndex}
                    <svg class="absolute left-1/2 -translate-x-1/2 -translate-y-[90%]" xmlns="http://www.w3.org/2000/svg" width="22" height="22" viewBox="0 0 22 22" fill="none">
                        <path d="M11.0003 1.83301C9.0554 1.83301 7.19014 2.60562 5.81488 3.98089C4.43961 5.35616 3.66699 7.22142 3.66699 9.16634C3.66699 14.1163 10.1295 19.708 10.4045 19.9463C10.5705 20.0884 10.7818 20.1664 11.0003 20.1664C11.2188 20.1664 11.4301 20.0884 11.5962 19.9463C11.917 19.708 18.3337 14.1163 18.3337 9.16634C18.3337 7.22142 17.561 5.35616 16.1858 3.98089C14.8105 2.60562 12.9452 1.83301 11.0003 1.83301ZM11.0003 18.0122C9.04783 16.1788 5.50033 12.228 5.50033 9.16634C5.50033 7.70765 6.07979 6.3087 7.11124 5.27725C8.14269 4.2458 9.54164 3.66634 11.0003 3.66634C12.459 3.66634 13.858 4.2458 14.8894 5.27725C15.9209 6.3087 16.5003 7.70765 16.5003 9.16634C16.5003 12.228 12.9528 16.188 11.0003 18.0122ZM11.0003 5.49967C10.2751 5.49967 9.56622 5.71472 8.96324 6.11762C8.36025 6.52052 7.89029 7.09317 7.61277 7.76317C7.33525 8.43316 7.26263 9.17041 7.40411 9.88167C7.54559 10.5929 7.89481 11.2463 8.4076 11.7591C8.92039 12.2719 9.57373 12.6211 10.285 12.7626C10.9963 12.904 11.7335 12.8314 12.4035 12.5539C13.0735 12.2764 13.6461 11.8064 14.049 11.2034C14.4519 10.6005 14.667 9.89154 14.667 9.16634C14.667 8.19388 14.2807 7.26125 13.5931 6.57362C12.9054 5.88598 11.9728 5.49967 11.0003 5.49967ZM11.0003 10.9997C10.6377 10.9997 10.2833 10.8921 9.98178 10.6907C9.68029 10.4893 9.44531 10.2029 9.30655 9.86793C9.16779 9.53293 9.13148 9.16431 9.20222 8.80867C9.27296 8.45304 9.44757 8.12637 9.70396 7.86998C9.96036 7.61358 10.287 7.43897 10.6427 7.36823C10.9983 7.29749 11.3669 7.3338 11.7019 7.47256C12.0369 7.61132 12.3232 7.84631 12.5247 8.1478C12.7261 8.44929 12.8337 8.80374 12.8337 9.16634C12.8337 9.65257 12.6405 10.1189 12.2967 10.4627C11.9529 10.8065 11.4866 10.9997 11.0003 10.9997Z" fill="black"/>
                    </svg>
                {/if}
                {#if question.marked}
                    <svg class="marked-icon absolute -right-[20%] -top-[20%]" xmlns="http://www.w3.org/2000/svg" width="15" height="15" viewBox="0 0 15 15" fill="none">
                        <path d="M10 1.25H5C3.9375 1.25 3.125 2.0625 3.125 3.125V13.125C3.125 13.25 3.125 13.3125 3.1875 13.4375C3.375 13.75 3.75 13.8125 4.0625 13.6875L7.5 11.6875L10.9375 13.6875C11.0625 13.75 11.125 13.75 11.25 13.75C11.625 13.75 11.875 13.5 11.875 13.125V3.125C11.875 2.0625 11.0625 1.25 10 1.25Z" fill="#FF66C4"/>
                    </svg>
                {/if}
                <button class="answer-box w-9 h-9 flex-shrink-0 font-semibold text-xl border border-black border-dashed hover:bg-[#e6e6e6]" class:answered-box={question.answer !== null} onclick={() => {setCurrentComponent(''); setQuestion(index);}}>{index + 1}</button>    
            </div>
        {/each}
    </div>
    <button class="font-semibold text-[15px] py-2 px-4 border-solid border-[#66E2FF] rounded-full text-[#66E2FF] hover:text-white hover:bg-[#66E2FF]" onclick={toReview}>Go to Review Page</button>
</div>
{/if}

<style>
    .nav-box {
        box-shadow: 0px 0px 7px 0px rgba(0, 0, 0, 0.25);
    }

    .answered-box {
        border: none;
        text-decoration: underline;
        background-color: #66E2FF;
    }

    .answered-box:hover {
        background-color: #07cbf7;
    }
</style>