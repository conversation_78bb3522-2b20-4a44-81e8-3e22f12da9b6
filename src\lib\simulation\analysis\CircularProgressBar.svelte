<!--
  @component
  ## CircularProgressBar
  A circular progress bar component for simulation analysis that displays progress with customizable colors, text, and smooth animations.

  ## Props
    - `score` (number): The current score/progress value
    - `total` (number): The maximum score/total value
    - `text` (string): The text to be displayed in the center of the progress bar
    - `size` (number): The size of the progress bar in pixels (default: 130)
    - `showPercentage` (boolean): Whether to show percentage instead of fraction (default: false)
    - `primaryColor` (string): Primary color for the background circle (default: "var(--sky-blue)")
    - `secondaryColor` (string): Secondary color for the progress circle (default: "var(--light-sky-blue)")
    - `animationDuration` (number): Duration of the fill animation in milliseconds (default: 1500)
    - `animationDelay` (number): Delay before animation starts in milliseconds (default: 0)

  ## Features
    - Smooth fill animation on mount using easeOutCubic easing
    - Animated score/percentage counter
    - Responsive design with mobile optimizations
    - Customizable colors and sizing
    - Automatic text truncation for long labels

  ## Usage
  ```html
  <CircularProgressBar
    score={75}
    total={100}
    text="Progress"
    size={150}
    showPercentage={true}
    primaryColor="var(--sky-blue)"
    secondaryColor="var(--light-sky-blue)"
    animationDuration={2000}
    animationDelay={500}
  />
  ```
-->

<script lang="ts">
  import { onMount } from 'svelte';

  interface Props {
    score: number;
    total: number;
    text: string;
    size?: number;
    showPercentage?: boolean;
    primaryColor?: string;
    secondaryColor?: string;
    animationDuration?: number;
    animationDelay?: number;
  }

  let {
    score,
    total,
    text,
    size = 130,
    showPercentage = false,
    primaryColor = "var(--sky-blue)",
    secondaryColor = "var(--light-sky-blue)",
    animationDuration = 1500,
    animationDelay = 0
  }: Props = $props();

  // Calculate target percentage and ensure it's between 0 and 1
  let targetPercentage = $derived(Math.min(Math.max(score / total, 0), 1));

  // Animated percentage that starts at 0 and animates to target
  let animatedPercentage = $state(0);
  let animatedScore = $state(0);

  // SVG circle calculations
  const stroke = 13;
  const center = size / 2;
  const radius = (size - stroke) / 2;
  const circumference = 2 * Math.PI * radius;

  // Shorten text if needed for better display
  let displayText = $derived(text.length > 12 ? text.substring(0, 10) + "..." : text);

  // Animation function using requestAnimationFrame for smooth animation
  function animateProgress() {
    const startTime = performance.now();
    const startPercentage = 0;
    const startScore = 0;

    function animate(currentTime: number) {
      const elapsed = currentTime - startTime - animationDelay;

      if (elapsed < 0) {
        requestAnimationFrame(animate);
        return;
      }

      const progress = Math.min(elapsed / animationDuration, 1);

      // Use easeOutCubic for smooth animation
      const easeOutCubic = 1 - Math.pow(1 - progress, 3);

      animatedPercentage = startPercentage + (targetPercentage - startPercentage) * easeOutCubic;
      animatedScore = Math.round(startScore + (score - startScore) * easeOutCubic);

      if (progress < 1) {
        requestAnimationFrame(animate);
      } else {
        // Ensure final values are exact
        animatedPercentage = targetPercentage;
        animatedScore = score;
      }
    }

    requestAnimationFrame(animate);
  }

  // Start animation when component mounts
  onMount(() => {
    animateProgress();
  });

  // Re-animate when score or total changes
  $effect(() => {
    if (targetPercentage !== animatedPercentage) {
      animateProgress();
    }
  });
</script>

<div class="circle-wrapper">
  <div class="box-shadow-wrapper">
    <svg
      class="svg"
      width={size}
      height={size}
      viewBox="0 0 {size + 4} {size + 4}"
      style="--center: {center}px; --primary-color: {primaryColor}; --secondary-color: {secondaryColor}"
    >
      <!-- Background circle -->
      <circle
        class="bg"
        cx={center}
        cy={center}
        r={radius}
        fill="none"
        stroke="var(--primary-color)"
        stroke-width={stroke}
      />

      <!-- Border circle for visual depth -->
      <circle
        class="border"
        cx={center}
        cy={center}
        r={radius}
        fill="none"
        stroke="var(--pitch-black)"
        stroke-width={stroke}
        stroke-dasharray={`${circumference * (1 - animatedPercentage) + 4} ${circumference * animatedPercentage - 4}`}
      />

      <!-- Progress circle -->
      <circle
        class="fg"
        cx={center}
        cy={center}
        r={radius}
        fill="none"
        stroke="var(--secondary-color)"
        stroke-width={stroke + 0.3}
        stroke-dasharray={`${circumference * (1 - animatedPercentage)} ${circumference * animatedPercentage}`}
      />
    </svg>

    <!-- Score/percentage display -->
    <div class="fraction">
      {#if showPercentage}
        <span>{Math.round(animatedPercentage * 100)}%</span>
      {:else}
        <span>{animatedScore}/{total}</span>
      {/if}
    </div>
  </div>

  <!-- Center text -->
  <div class="middle-text">
    {displayText}
  </div>
</div>

<style>
  .circle-wrapper {
    position: relative;
    display: flex;
    width: fit-content;
  }

  /* Progress circle styling */
  circle.fg {
    rotate: -90deg;
    transform-origin: var(--center) var(--center);
    transition: stroke-dasharray 0.3s ease-in-out;
  }

  circle.border {
    rotate: -91.7deg;
    transform-origin: var(--center) var(--center);
  }

  /* SVG border effect using drop-shadow */
  .svg {
    filter: drop-shadow(-1px 0 var(--pitch-black))
            drop-shadow(0 -1px var(--pitch-black))
            drop-shadow(0 1px var(--pitch-black))
            drop-shadow(1px 0 var(--pitch-black));
  }

  /* Box shadow for the entire component */
  .box-shadow-wrapper {
    filter: drop-shadow(0.25rem 0.25rem var(--pitch-black));
  }

  /* Center text styling */
  .middle-text {
    font-family: "Inter";
    font-size: 1.125rem;
    font-weight: 600;
    position: absolute;
    top: 50%;
    left: 0;
    transform: translate(0, -50%);
    width: 100%;
    text-align: center;
    color: var(--pitch-black);
    pointer-events: none;
  }

  /* Score/percentage display */
  .fraction {
    display: flex;
    justify-content: center;
    align-items: center;
    padding: 0.25rem;
    border: 1px solid var(--pitch-black);
    border-radius: 0.25rem;
    position: absolute;
    right: 2px;
    bottom: 18px;
    min-width: 47px;
    min-height: 25px;
    background-color: white;
    font-family: "Inter";
    font-size: 0.875rem;
    font-weight: 450;
    color: var(--pitch-black);
  }

  /* Responsive adjustments */
  @media (max-width: 540px) {
    .middle-text {
      font-size: 1rem;
    }

    .fraction {
      font-size: 0.75rem;
      min-width: 40px;
      min-height: 22px;
    }
  }
</style>