<script lang="ts">
	import { P1 } from "$lib/ui";
	import CircularProgressBar from "./CircularProgressBar.svelte";
    import EstimatedScore from "./EstimatedScore.svelte";

    const tabColors = {
        "Overview": "var(--light-aquamarine)", "Skills Assessment": "var(--light-tangerine)",
        "Summary": "var(--light-sky-blue)"
    }

    let currentTab = $state("Overview");
</script>

<div class="container w-[85%] h-[90%] my-auto flex">
    <div class="nav-container h-full flex flex-col gap-2">
        {#each Object.keys(tabColors) as item}
            <button class="nav-item border border-black border-solid py-2 px-5 relative rounded-l-full translate-x-[1px] overflow-hidden" style:background-color={tabColors[item]} onclick={() => currentTab = item} class:tab-active={currentTab === item} style:border-right={currentTab === item ?  `1px solid ${tabColors[item]}` : "1px solid black"}>
                <P1 isBold>{item}</P1>
                <div class="button-overlay w-full h-full bg-black opacity-10 absolute top-0 left-0 hover:opacity-0" class:hidden={currentTab === item}></div>
            </button>
        {/each}
    </div>
    <div class="content-container flex-1 h-full border border-black p-4 rounded-lg rounded-tl-none" style:background-color={tabColors[currentTab]}>
        {@render content?.()}
    </div>
</div>


{#snippet content()}
{#if currentTab === "Overview"}
    <EstimatedScore />
{:else if currentTab === "Skills Assessment"}
    <P1>Skills Assessment</P1>
    <CircularProgressBar score={0} total={10} text="Module 1" />
{:else if currentTab === "Summary"}
    <P1>Summary</P1>
{/if}
{/snippet}

<style>
    .tab-active {
        box-shadow: 0 0.25rem 0.25rem var(--pitch-black);
    }

    .content-container {
        box-shadow: 0.25rem 0.25rem 0.25rem var(--pitch-black);
    }
</style>