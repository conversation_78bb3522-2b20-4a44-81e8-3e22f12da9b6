<script lang="ts">
    let {
        currentQuestion,
        currentStudentData,
        setAnswer = null,
        setCrossed = null,
        isEliminateTool = false,
        isWalkthrough = false
    } = $props();
</script>

{#if !isWalkthrough}
    <div class="multiple-choices flex flex-col gap-4">
        {#each currentQuestion.answerChoices as choice, index}
        {@const letters = ['A', 'B', 'C', 'D']}
            <div class="choice-wrapper flex gap-3 items-center">
                <button class="choice-container flex items-center w-full p-3 rounded-lg border-[#333] border-solid hover:bg-[lightgray] text-[#333]" class:answer-border={currentStudentData.answer === index} class:crossed={currentStudentData.crossed[index]} onclick={() => setAnswer(index)}>
                    <div class="choice-content flex items-center gap-4 w-full" class:opacity-[0.6]={currentStudentData.crossed[index]}>
                        <div class="border-2 border-solid rounded-full border-[#333] w-7 h-7 flex items-center justify-center flex-shrink-0">
                            <span class="select-none font-['Inter'] font-bold">{letters[index]}</span>
                        </div>

                        <div class="choice-text flex-1">
                            {#if Array.isArray(choice)}
                                <div class="choice-table-wrapper">
                                    <table class="table table--choice">
                                        <tbody>
                                            {#each choice as row }
                                            <tr>
                                                {#each row as item }
                                                    <td>{@html item}</td>
                                                {/each}
                                            </tr>
                                        {/each}
                                        </tbody>
                                    </table>
                                </div>
                            {:else}
                                <p class="font-normal select-text text-start">{@html choice}</p>
                            {/if}
                        </div>
                    </div>
                </button>

                {#if isEliminateTool}
                    {#if currentStudentData.crossed[index]}
                        <button class="cross-text select-none text-sm underline hover:no-underline font-['Inter'] font-semibold" onclick={() => setCrossed(index)}>Undo</button>
                    {:else}
                        <button class="cross-choice select-none w-5 h-5 rounded-full border-black border-solid border flex items-center justify-center mx-2 flex-shrink-0 text-xs hover:bg-[lightgray] relative after:content-[''] after:absolute after:w-[150%] after:bg-black after:h-[1.5px] font-['Inter'] font-semibold" onclick={() => {setCrossed(index)}}>{letters[index]}</button>
                    {/if}
                {/if}
            </div>
        {/each}
    </div>
{:else}
    <div class="multiple-choices flex flex-col gap-4">
        {#each currentQuestion.answerChoices as choice, index}
        {@const letters = ['A', 'B', 'C', 'D']}
            <button class="choice-container flex items-center w-full p-3 rounded-lg border-[#333] border-solid text-[#333] !cursor-default" class:bg-[#D1FFEE]={Number(currentQuestion.correctAnswer) === index}>
                <div class="choice-content flex items-center gap-4 w-full">
                    <div class="border-2 border-solid rounded-full border-[#333] w-7 h-7 flex items-center justify-center flex-shrink-0" class:bg-[#42FFB7]={Number(currentQuestion.correctAnswer) === index}>
                        <span class="select-none font-['Inter'] font-bold">{letters[index]}</span>
                    </div>

                    <div class="choice-text flex-1">
                        {#if Array.isArray(choice)}
                            <div class="choice-table-wrapper">
                                <table class="table table--choice">
                                    <tbody>
                                        {#each choice as row }
                                        <tr>
                                            {#each row as item }
                                                <td>{@html item}</td>
                                            {/each}
                                        </tr>
                                    {/each}
                                    </tbody>
                                </table>
                            </div>
                        {:else}
                            <p class="font-normal select-text text-start">{@html choice}</p>
                        {/if}
                    </div>
                </div>
            </button>
        {/each}
    </div>
{/if}

<style>
    .crossed {
        position: relative;
    }

    .crossed::after {
        content: '';
        position: absolute;
        width: 102%;
        background-color: black;
        height: 2px;
        left: -1%;
    }

    .answer-border {
        outline: 3px solid #66E2FF;
    }

    .choice-table-wrapper {
        margin: 12px 0;
    }

    .table {
        width: 100%;
        text-align: center;
        border: 1px solid black;
        border-collapse: collapse;
    }

    .table tr {
        width: 50%;
        border: 1px solid black;
        border-collapse: collapse;
    }

    .table td {
        padding: 14px 10px;
        border: 1px solid black;
        border-collapse: collapse;
        color: var(--Charcoal, #333);
    }

    .table--choice td {
        padding: 13px 22px;
    }
</style>