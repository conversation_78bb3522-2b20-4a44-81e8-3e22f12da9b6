import { supabase } from "$lib/server";
import { error } from '@sveltejs/kit';


export const load = async ({ params, locals }) => {
    const { data: simulationData, error: simulationError } = await supabase
        .from('simulations')
        .select('*')
        .eq('slug', params.slug)
        .maybeSingle();

    if (!simulationData) error(404, "Simulation not found");

    const modules: { id: string, title: string, questions: any[] }[] = [
        {
            id: "RW1",
            title: "R&W - Module 1",
            questions: simulationData.RW1
        },
        {
            id: "RW2",
            title: "R&W - Module 2",
            questions: simulationData.RW2
        },
        {
            id: "M1",
            title: "Math - Module 1",
            questions: simulationData.M1
        },
        {
            id: "M2",
            title: "Math - Module 2",
            questions: simulationData.M2
        }
    ];
    
    await Promise.all(modules.map(async (module) => {
        module.questions = await Promise.all(module.questions.map(async (question) => {
            const { data: questionData, error: questionError } = await supabase
                .from('question')
                .select('topic, questionType, intro, passage, passage2, question, answerChoices, correctAnswer')
                .eq('id', question)
                .single();
    
            if (questionError) throw new Error(questionError.message);

            if (questionData.questionType === "Student Notes") {
                questionData.passage = questionData.passage.split(" | ");
            }

            return questionData;
        }));
    }));

    return { title: simulationData.title, modules, slug: params.slug, uid: locals.uid };
}