import { writable } from "svelte/store";

export const currentAnnotate = writable<Annotate | null>(null);

export const colors = {
    yellow: { normal: "#fff59d", focus: "#ffc739ff" },
    pink:   { normal: "#f8bbd0", focus: "#f48fb1" },
    blue:   { normal: "#b3e5fc", focus: "#4fc3f7" },
};


export class AnnotateManager {
    static blockTags = new Set([
        "ADDRESS","ARTICLE","ASIDE","BLOCKQUOTE","CANVAS","DD","DIV",
        "DL","DT","FIELDSET","FIGCAPTION","FIGURE","FOOTER","FORM",
        "H1","H2","H3","H4","H5","H6","HEADER","HR","LI","MAIN","NAV",
        "NOSCRIPT","OL","P","<PERSON>E","SECTION","TABLE","TFOOT","UL","VIDEO"
    ]);

    static #currentAnnotate: Annotate | null = null;
    static #clickHandler: (event: MouseEvent) => void;
    static excludedList: HTMLElement[] = [];

    static get currentAnnotate() {
        return this.#currentAnnotate;
    }

    static set currentAnnotate(annotate: Annotate | null) {
        this.#currentAnnotate = annotate;
        currentAnnotate.set(annotate);
        if (annotate) {
            this.#clickHandler = (event: MouseEvent) => {
                const target = event.target as HTMLElement;
                if (this.excludedList.some(element => element.contains(target))) return;
                AnnotateManager.currentAnnotate?.setFocus(null);
            };

            document.addEventListener("click", this.#clickHandler, { capture: true });
        } else {
            document.removeEventListener("click", this.#clickHandler, { capture: true });
        }
    }

    #annotateList: Annotate[];

    constructor(rootList: HTMLElement[]) {
        this.#annotateList = rootList.map(root => new Annotate(root));
    }

    highlight(color: string = "yellow"): boolean {
        const selection = document.getSelection();
        if (!selection || !selection.rangeCount || selection.isCollapsed) return false;

        const range = selection.getRangeAt(0);

        const span = document.createElement('span');
        span.appendChild(range.cloneContents());

        function containBlockTag(element) {
            for (const el of element.querySelectorAll("*")) {
                if (AnnotateManager.blockTags.has(el.tagName.toUpperCase())) return true;
            }
            return false;
        }

        if (containBlockTag(span)) return false;
        return this.#annotateList?.some(annotate => annotate.highlight(color)) ?? false;
    }

    setRoot(rootList: HTMLElement[]) {
        this.#annotateList?.forEach((annotate, i) => annotate.setRoot(rootList[i]));
    }

    dispose() {
        this.#annotateList?.forEach(annotate => annotate.dispose());
    }
}

class Annotate {
    root: HTMLElement;
    positions: number[];
    marks: Map<string, Mark>;
    highlightColors: Map<string, string>;
    highlightToFloatingDiv: Map<string, HTMLDivElement>;
    focusedHighlight: [number, number] | null;

    constructor(rootElement: HTMLElement) {
        this.root = rootElement;
        this.positions = [];
        this.marks = new Map();
        this.highlightColors = new Map();
        this.highlightToFloatingDiv = new Map();
        this.focusedHighlight = null;
    }

    // Get the total offset of a target node and offset within the root node
    getTotalOffset(targetNode, targetOffset) {
        const iterator = document.createNodeIterator(this.root, NodeFilter.SHOW_TEXT);
        let currentNode;
        let totalOffset = 0;
        while ((currentNode = iterator.nextNode())) {
            if (currentNode === targetNode) return totalOffset + targetOffset;
            totalOffset += currentNode.textContent.length;
        }
        return -1; // Not found
    }

    // Get the Range of a given Start and End Position within the Root Node
    getRange(pos: [number, number]) {
        const [startPos, endPos] = pos;
        let charCount = 0;
        let startNode, startNodeOffset;
        let endNode, endNodeOffset;
    
        const iterator = document.createNodeIterator(this.root, NodeFilter.SHOW_TEXT);
    
        let currentNode;
        while ((currentNode = iterator.nextNode())) {
            const nextCharCount = charCount + currentNode.textContent.length;
            if (!startNode && startPos >= charCount && startPos < nextCharCount) {
                startNode = currentNode;
                startNodeOffset = startPos - charCount;
            }
            if (!endNode && endPos > charCount && endPos <= nextCharCount) {
                endNode = currentNode;
                endNodeOffset = endPos - charCount;
                break;
            }
            charCount = nextCharCount;
        }
    
        if (!startNode || !endNode) {
            console.warn("Offsets out of range");
            return null;
        }
    
        const range = document.createRange();
        range.setStart(startNode, startNodeOffset);
        range.setEnd(endNode, endNodeOffset);

        return range;
    }
    
    // Create a mark element with the given start and end offsets within the root node
    createMark(markPos: [number, number], oldMark?: Mark): Mark {
        const markKey = `${markPos[0]},${markPos[1]}`;

        const range = this.getRange(markPos);
        const extracted = range.extractContents();
        const markElement = document.createElement("mark");
        markElement.appendChild(extracted);
        range.insertNode(markElement);

        const mark = new Mark(markElement, oldMark);
        mark.setElementColor();

        this.marks.set(markKey, mark);
        this.addMarkEvents(mark);

        return this.marks.get(markKey);
    }
    
    // Remove a mark element with the given start and end offsets within the root node
    removeMark(markPos: [number, number]) {
        const markKey = `${markPos[0]},${markPos[1]}`;
        this.marks.get(markKey).delete();
        this.marks.delete(markKey);
    }

    // Split the mark element into two marks at the given position
    splitMarks(markPos: [number, number], position) {
        const splittedMark = this.marks.get(`${markPos[0]},${markPos[1]}`);
        this.removeMark(markPos);
        this.createMark([markPos[0], position], splittedMark);
        this.createMark([position, markPos[1]], splittedMark);
    }

    // Merge two mark elements into one
    mergeMarks(markPos: [number, number], position) {
        let mergedMark = this.marks.get(`${markPos[0]},${position}`);
        this.removeMark([markPos[0], position]);
        this.removeMark([position, markPos[1]]);
        this.createMark(markPos, mergedMark);
    }

    // Create floating div
    createFloatingDiv(highlightPos: [number, number] | null, textContent: string) {
        if (!highlightPos) return;
        const floatingStyles = {
            position: 'absolute',
            // bottom: '100%', // place above the span
            // left: '0',      // align with left edge (start)
            // transform: 'translateY(-8px)', // small gap above
            display: 'inline-flex',
            minWidth: '100px',
            padding: '5px',
            justifyContent: 'center',
            alignItems: 'center',
            gap: '10px',
            borderRadius: '10px',
            border: '2px solid #000',
            background: '#FFF',
            color: '#000',
            fontFamily: 'Inter',
            fontSize: '17px',
            fontStyle: 'normal',
            fontWeight: '500',
            lineHeight: 'normal'
        };

        // Create a floating div
        const floatingDiv = document.createElement("div");
        Object.assign(floatingDiv.style, floatingStyles);
        floatingDiv.textContent = textContent;

        const range = this.getRange(highlightPos);
        const rect = range.getBoundingClientRect();
        floatingDiv.style.display = "block";
        document.body.appendChild(floatingDiv);
        const divWidth = floatingDiv.offsetWidth;
        const divHeight = floatingDiv.offsetHeight;
        floatingDiv.style.display = "none"; // hide initially

        floatingDiv.style.left = `${rect.left + rect.width / 2 - divWidth / 2 + window.scrollX}px`;
        floatingDiv.style.top = `${rect.top - divHeight - 20 + window.scrollY}px`; // 8px above the target

        this.highlightToFloatingDiv.set(`${highlightPos[0]},${highlightPos[1]}`, floatingDiv);
    }

    // Add events to mark
    addMarkEvents(mark: Mark) {
        const element = mark.element;

        // Add hover event
        element.addEventListener("mouseenter", () => {
            this.updateHighlight(mark.minHighlight, true);
            const floatingDiv = this.highlightToFloatingDiv.get(`${mark.minHighlight[0]},${mark.minHighlight[1]}`);
            if (floatingDiv.textContent !== "") {
                const range = this.getRange(mark.minHighlight);
                const rect = range.getBoundingClientRect();
                floatingDiv.style.display = "block";
                floatingDiv.style.left = `${rect.left + rect.width / 2 - floatingDiv.offsetWidth / 2 + window.scrollX}px`;
                floatingDiv.style.top = `${rect.top - floatingDiv.offsetHeight - 20 + window.scrollY}px`;
            }
        });
        element.addEventListener("mouseleave", () => {
            this.updateHighlight(mark.minHighlight, false);
            const floatingDiv = this.highlightToFloatingDiv.get(`${mark.minHighlight[0]},${mark.minHighlight[1]}`);
            floatingDiv.style.display = "none";
        });

        // Add click event
        element.addEventListener("click", () => {
            this.setFocus(mark.minHighlight);
        });
    }

    // Create new highlight
    createHighlight(highlightPos: [number, number]) {
        const [startPos, endPos] = highlightPos;
        let startIndex = this.positions.findIndex(x => x >= startPos);
        let newStartPos = true;

        if (startIndex === -1) {
            this.positions.push(startPos);
            startIndex = this.positions.length-1;
        } else if (this.positions[startIndex] !== startPos) {
            this.positions.splice(startIndex, 0, startPos);
        } else {
            newStartPos = false;
        }

        if (newStartPos && startIndex > 0 && startIndex < this.positions.length - 1 && this.marks.has(`${this.positions[startIndex - 1]},${this.positions[startIndex + 1]}`)) {
            this.splitMarks([this.positions[startIndex - 1], this.positions[startIndex + 1]], startPos);
        }

        let endIndex = this.positions.findIndex(x => x >= endPos);
        let newEndPos = true;

        if (endIndex === -1) {
            this.positions.push(endPos);
            endIndex = this.positions.length - 1;
        } else if (this.positions[endIndex] !== endPos) {
            this.positions.splice(endIndex, 0, endPos);
        } else {
            newEndPos = false;
        }

        if (newEndPos && endIndex < this.positions.length - 1 && this.marks.has(`${this.positions[endIndex - 1]},${this.positions[endIndex + 1]}`)) {
            this.splitMarks([this.positions[endIndex - 1], this.positions[endIndex + 1]], endPos);
        }

        for (let i = startIndex; i < endIndex; i++) {
            const markKey = `${this.positions[i]},${this.positions[i + 1]}`;
            const mark = this.marks.get(markKey) ?? this.createMark([this.positions[i], this.positions[i + 1]]);
            mark.addHighlight(highlightPos);
            mark.updateColor(this.highlightColors.get(`${mark.minHighlight[0]},${mark.minHighlight[1]}`));
        }
    }


    // Normalize the DOM tree
    normalizeDeep(root: HTMLElement) {
        let prev = null;

        let i = 0;
        while (i < root.childNodes.length) {
            const node = root.childNodes[i];
            if (node instanceof HTMLElement) {
                // Recursively normalize children first
                this.normalizeDeep(node);

                // // Remove empty elements
                // if (node.childNodes.length === 0) {
                //     root.removeChild(node);
                //     continue;
                // }

                // Merge adjacent elements with same tag and no attributes
                if (prev &&
                    prev instanceof HTMLElement &&
                    prev.tagName === node.tagName &&
                    prev.attributes.length === 0 &&
                    node.attributes.length === 0
                ) {
                    while (node.firstChild) {
                        prev.appendChild(node.firstChild);
                    }
                    root.removeChild(node);
                    continue;
                }
            }

            // Save this node as previous
            prev = node;
            i++;
        }

        root.normalize();
    }

    // Normalize the positions array and the marks within the root node
    normalizeMarks(startIndex, endIndex) {
        // Remove marks that have no highlights
        for (let i = startIndex; i < endIndex; i++) {
            const mark = this.marks.get(`${this.positions[i]},${this.positions[i + 1]}`);
            if (mark.highlights.length === 0) this.removeMark([this.positions[i], this.positions[i + 1]]);

        }

        // Remove positions that are not in use
        for (let i = startIndex; i <= endIndex; i++) {
            if (startIndex === endIndex) {
                this.positions.splice(i, 1);
            }

            const prev = this.positions[i - 1];
            const curr = this.positions[i];
            const next = this.positions[i + 1];

            const hasPrev = prev !== undefined && this.marks.has(`${prev},${curr}`);
            const hasNext = next !== undefined && this.marks.has(`${curr},${next}`);

            if (!hasPrev && !hasNext) {
                this.positions.splice(i, 1);
                i--;
                endIndex--;
            }
        }

        // Merge marks that belong to same highlights
        let prev = null;
        for (let i = startIndex - 1; i <= endIndex; i++) {
            const cur = this.positions[i];
            const next = this.positions[i + 1];
            if (cur === undefined || next === undefined || !this.marks.has(`${cur},${next}`)) {
                prev = null;
                continue;
            }

            if (prev === null) {
                prev = cur;
                continue;
            }

            const mark1 = this.marks.get(`${prev},${cur}`);
            const mark2 = this.marks.get(`${cur},${next}`);
            if (mark1.equal(mark2)) {
                this.mergeMarks([prev, next], cur);
                this.positions.splice(i, 1);
                i--;
                endIndex--;
            } else {
                prev = cur;
            }
        }

        this.normalizeDeep(this.root);
    }

    // Remove a highlight from the mark element
    removeHighlight(highlightPos: [number, number]) {
        const key = `${highlightPos[0]},${highlightPos[1]}`;
        this.highlightColors.delete(key);
        this.highlightToFloatingDiv.get(key)?.remove();
        this.highlightToFloatingDiv.delete(key);

        let startIndex = this.positions.findIndex(x => x === highlightPos[0]);
        let endIndex = this.positions.findIndex(x => x === highlightPos[1]);
        for (let i = startIndex; i < endIndex; i++) {
            this.marks.get(`${this.positions[i]},${this.positions[i + 1]}`).removeHighlight(highlightPos);
        }

        this.normalizeMarks(startIndex, endIndex);
    }

    // Highlight the selected text
    highlight(color: string = "yellow"): boolean {
        // Get the selection
        if (this.root === null) return false;
        const selection = document.getSelection();
        const range = selection.getRangeAt(0);

        if (!this.root.contains(range.commonAncestorContainer)) return false;

        // Get the total offset of the start and end of the range
        const startPos = this.getTotalOffset(range.startContainer, range.startOffset);
        const endPos = this.getTotalOffset(range.endContainer, range.endOffset);

        const highlightPos: [number, number] = [startPos, endPos];

        this.highlightColors.set(`${startPos},${endPos}`, color);
        this.createFloatingDiv(highlightPos, "");
        this.createHighlight(highlightPos);
        this.setFocus(highlightPos);

        // Collapse the range to the end
        range.collapse(false);
        return true;
    }

    // Update highlight color: focus | blur
    updateHighlight(highlightPos: [number, number] | null, focus: boolean) {
        if (!highlightPos) return;

        const highlightColor = this.highlightColors.get(`${highlightPos[0]},${highlightPos[1]}`);
        const currentFocusColor = this.focusedHighlight ? colors[this.highlightColors.get(`${this.focusedHighlight[0]},${this.focusedHighlight[1]}`)].focus : null;

        this.positions.slice(this.positions.indexOf(highlightPos[0]), this.positions.indexOf(highlightPos[1]) + 1).forEach((pos, i, tempPositions) => {
            if (i === tempPositions.length - 1) return;
            const tempMark = this.marks.get(`${pos},${tempPositions[i + 1]}`);

            const appliedColor = focus ? colors[highlightColor].focus : currentFocusColor && this.focusedHighlight[0] <= pos && this.focusedHighlight[1] >= tempPositions[i + 1] ? currentFocusColor : colors[tempMark.color].normal;

            tempMark.setElementColor(appliedColor);
        });
    }

    setFocus(highlightPos: [number, number] | null) {
        const oldFocus = this.focusedHighlight;
        this.focusedHighlight = highlightPos;
        this.updateHighlight(oldFocus, false);
        this.updateHighlight(this.focusedHighlight, true);
        AnnotateManager.currentAnnotate = highlightPos ? this : null;
    }

    changeHighlightColor(color: string) {
        if (!this.focusedHighlight) return;
        this.highlightColors.set(`${this.focusedHighlight[0]},${this.focusedHighlight[1]}`, color);
        this.positions.slice(this.positions.indexOf(this.focusedHighlight[0]), this.positions.indexOf(this.focusedHighlight[1]) + 1).forEach((pos, i, tempPositions) => {
            if (i === tempPositions.length - 1) return;
            const tempMark = this.marks.get(`${pos},${tempPositions[i + 1]}`);
            if (this.focusedHighlight[0] === tempMark.minHighlight[0] && this.focusedHighlight[1] === tempMark.minHighlight[1]) tempMark.updateColor(color);
        });
        this.setFocus(this.focusedHighlight);
    }

    getCommentText() {
        if (this.focusedHighlight) {
            const floatingDiv = this.highlightToFloatingDiv.get(`${this.focusedHighlight[0]},${this.focusedHighlight[1]}`);
            return floatingDiv.textContent;
        }
        return null;
    }

    setCommentText(text: string) {
        const floatingDiv = this.highlightToFloatingDiv.get(`${this.focusedHighlight[0]},${this.focusedHighlight[1]}`);
        floatingDiv.textContent = text;
    }

    getSelectedText() {
        const range = this.getRange(this.focusedHighlight);
        return range.toString();
    }

    deleteHighlight() {
        const oldFocus = this.focusedHighlight;
        this.setFocus(null);
        this.removeHighlight(oldFocus);
    }

    rerenderHighlights() {
        this.positions.forEach((cur, i) => {
            if (i === this.positions.length - 1) return;
            if (this.marks.has(`${cur},${this.positions[i + 1]}`)) this.createMark([cur, this.positions[i + 1]], this.marks.get(`${cur},${this.positions[i + 1]}`));
        });
    }

    setRoot(rootElement: HTMLElement) {
        this.root = rootElement;
        this.rerenderHighlights();
    }

    dispose() {
        for (const mark of this.marks.values()) mark.delete();
        for (const floatingDiv of this.highlightToFloatingDiv.values()) floatingDiv.remove();
        this.highlightToFloatingDiv.clear();
        this.highlightColors.clear();
        this.root = null;
    }
}


class Mark {
    element: HTMLElement;
    highlights: [number, number][];
    color: string | null;
    minHighlight: [number, number] | null;

    constructor(element: HTMLElement, oldMark?: Mark) {
        this.element = element;
        this.highlights = oldMark ? structuredClone(oldMark.highlights) : [];
        this.color = oldMark?.color ?? null;
        this.minHighlight = oldMark ? structuredClone(oldMark.minHighlight) : null;
    }

    getMinHighlight(): [number, number] {
        if (!this.highlights.length) return null;
        return this.highlights.reduce((min: [number, number], item: [number, number]) => min[1] - min[0] < item[1] - item[0] ? min : item, this.highlights[0]);
    }

    addHighlight(highlight: [number, number]) {
        this.highlights.push(highlight);
        this.minHighlight = !this.minHighlight || this.minHighlight[1] - this.minHighlight[0] > highlight[1] - highlight[0] ? highlight : this.minHighlight;
    }

    removeHighlight(highlight: [number, number]) {
        this.highlights = this.highlights.filter(item => item[0] !== highlight[0] || item[1] !== highlight[1]);
        this.minHighlight = this.getMinHighlight();
    }

    updateColor(color: string) {
        this.color = color;
        this.setElementColor(colors[color].normal);
    }

    setElementColor(color?: string) {
        this.element.style.backgroundColor = color ?? (this.color ? colors[this.color].normal : "");
    }

    equal(other: Mark) {
        return this.highlights.length === other.highlights.length && this.highlights.every(item => other.highlights.some(item2 => item[0] === item2[0] && item[1] === item2[1]));
    }

    delete() {
        const parent = this.element.parentNode;
        while (this.element.firstChild) {
            parent.insertBefore(this.element.firstChild, this.element);
        }
        parent.removeChild(this.element);
    }
}