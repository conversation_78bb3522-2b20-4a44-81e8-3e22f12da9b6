<!-- 
  @component
  ## CircularProgressBarDemo
  A demo component showcasing different configurations of the CircularProgressBar component.
  This is useful for testing and demonstrating the component's capabilities.
-->

<script lang="ts">
  import CircularProgressBar from './CircularProgressBar.svelte';
  import { H3, H4, P1 } from '$lib/ui';

  // Demo data
  let demoConfigs = [
    {
      title: "Basic Progress",
      score: 75,
      total: 100,
      text: "Progress",
      showPercentage: false,
      primaryColor: "var(--sky-blue)",
      secondaryColor: "var(--light-sky-blue)"
    },
    {
      title: "Percentage Display",
      score: 85,
      total: 100,
      text: "Score",
      showPercentage: true,
      primaryColor: "var(--aquamarine)",
      secondaryColor: "var(--light-aquamarine)"
    },
    {
      title: "Math Section",
      score: 18,
      total: 22,
      text: "Math",
      showPercentage: false,
      primaryColor: "var(--rose)",
      secondaryColor: "var(--light-rose)"
    },
    {
      title: "Reading & Writing",
      score: 23,
      total: 27,
      text: "R&W",
      showPercentage: true,
      primaryColor: "var(--purple)",
      secondaryColor: "var(--light-purple)"
    },
    {
      title: "Large Size",
      score: 92,
      total: 100,
      text: "Overall",
      showPercentage: true,
      size: 180,
      primaryColor: "var(--tangerine)",
      secondaryColor: "var(--light-tangerine)"
    },
    {
      title: "Small Size",
      score: 45,
      total: 60,
      text: "Vocab",
      showPercentage: false,
      size: 100,
      primaryColor: "var(--yellow)",
      secondaryColor: "var(--light-yellow)"
    }
  ];

  // Interactive demo controls
  let interactiveScore = $state(75);
  let interactiveTotal = $state(100);
  let interactiveText = $state("Interactive");
  let interactiveShowPercentage = $state(true);
  let interactiveSize = $state(130);
</script>

<div class="demo-container">
  <H3>CircularProgressBar Demo</H3>
  
  <div class="demo-section">
    <H4>Predefined Examples</H4>
    <P1>Here are various configurations of the CircularProgressBar component:</P1>
    
    <div class="examples-grid">
      {#each demoConfigs as config}
        <div class="example-item">
          <div class="example-title">{config.title}</div>
          <CircularProgressBar 
            score={config.score}
            total={config.total}
            text={config.text}
            showPercentage={config.showPercentage}
            size={config.size || 130}
            primaryColor={config.primaryColor}
            secondaryColor={config.secondaryColor}
          />
          <div class="example-details">
            {config.score}/{config.total} 
            {config.showPercentage ? `(${Math.round(config.score / config.total * 100)}%)` : ''}
          </div>
        </div>
      {/each}
    </div>
  </div>

  <div class="demo-section">
    <H4>Interactive Demo</H4>
    <P1>Adjust the controls below to see how the component responds:</P1>
    
    <div class="interactive-demo">
      <div class="controls">
        <div class="control-group">
          <label for="score">Score:</label>
          <input 
            id="score" 
            type="range" 
            min="0" 
            max={interactiveTotal} 
            bind:value={interactiveScore}
          />
          <span>{interactiveScore}</span>
        </div>
        
        <div class="control-group">
          <label for="total">Total:</label>
          <input 
            id="total" 
            type="range" 
            min="1" 
            max="200" 
            bind:value={interactiveTotal}
          />
          <span>{interactiveTotal}</span>
        </div>
        
        <div class="control-group">
          <label for="text">Text:</label>
          <input 
            id="text" 
            type="text" 
            bind:value={interactiveText}
            maxlength="15"
          />
        </div>
        
        <div class="control-group">
          <label for="size">Size:</label>
          <input 
            id="size" 
            type="range" 
            min="80" 
            max="250" 
            bind:value={interactiveSize}
          />
          <span>{interactiveSize}px</span>
        </div>
        
        <div class="control-group">
          <label for="percentage">Show Percentage:</label>
          <input 
            id="percentage" 
            type="checkbox" 
            bind:checked={interactiveShowPercentage}
          />
        </div>
      </div>
      
      <div class="interactive-result">
        <CircularProgressBar 
          score={interactiveScore}
          total={interactiveTotal}
          text={interactiveText}
          showPercentage={interactiveShowPercentage}
          size={interactiveSize}
          primaryColor="var(--sky-blue)"
          secondaryColor="var(--light-sky-blue)"
        />
      </div>
    </div>
  </div>
</div>

<style>
  .demo-container {
    padding: 2rem;
    max-width: 1200px;
    margin: 0 auto;
  }

  .demo-section {
    margin-bottom: 3rem;
  }

  .examples-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 2rem;
    margin-top: 1.5rem;
  }

  .example-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 1rem;
    padding: 1.5rem;
    border: 1px solid var(--pitch-black);
    border-radius: 0.5rem;
    background-color: white;
    box-shadow: 0.25rem 0.25rem var(--pitch-black);
  }

  .example-title {
    font-family: "Inter";
    font-weight: 600;
    font-size: 1.125rem;
    text-align: center;
  }

  .example-details {
    font-family: "Inter";
    font-size: 0.875rem;
    color: var(--charcoal);
    text-align: center;
  }

  .interactive-demo {
    display: flex;
    gap: 3rem;
    margin-top: 1.5rem;
    align-items: flex-start;
  }

  .controls {
    flex: 1;
    display: flex;
    flex-direction: column;
    gap: 1rem;
    padding: 1.5rem;
    border: 1px solid var(--pitch-black);
    border-radius: 0.5rem;
    background-color: var(--light-sky-blue);
  }

  .control-group {
    display: flex;
    align-items: center;
    gap: 1rem;
  }

  .control-group label {
    font-family: "Inter";
    font-weight: 500;
    min-width: 100px;
  }

  .control-group input[type="range"] {
    flex: 1;
  }

  .control-group input[type="text"] {
    flex: 1;
    padding: 0.5rem;
    border: 1px solid var(--pitch-black);
    border-radius: 0.25rem;
    font-family: "Inter";
  }

  .control-group span {
    font-family: "Inter";
    font-weight: 500;
    min-width: 50px;
    text-align: right;
  }

  .interactive-result {
    display: flex;
    justify-content: center;
    align-items: center;
    padding: 2rem;
    border: 1px solid var(--pitch-black);
    border-radius: 0.5rem;
    background-color: white;
    box-shadow: 0.25rem 0.25rem var(--pitch-black);
  }

  @media (max-width: 768px) {
    .interactive-demo {
      flex-direction: column;
      gap: 1.5rem;
    }
    
    .examples-grid {
      grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
      gap: 1rem;
    }
    
    .control-group {
      flex-direction: column;
      align-items: flex-start;
      gap: 0.5rem;
    }
    
    .control-group label {
      min-width: auto;
    }
  }
</style>
